import React, { useState, useEffect, useContext } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    Alert,
    RefreshControl
} from 'react-native';
import { <PERSON>con, <PERSON><PERSON>, Card } from 'react-native-elements';
import { colors } from '../common/theme';
import { fonts } from '../common/font';
import i18n from 'i18n-js';
import { useSelector, useDispatch } from 'react-redux';
import { api } from 'common';
import { FirebaseContext } from 'common';
import { GroupedDeliveryDetails } from '../components';
import ProductChecklist from '../components/ProductChecklist';
import { onValue, off } from 'firebase/database';

export default function GroupedDeliveryScreen(props) {
    const { t } = i18n;
    const isRTL = i18n.locale.indexOf('he') === 0 || i18n.locale.indexOf('ar') === 0;
    
    const dispatch = useDispatch();
    const auth = useSelector(state => state.auth);
    const firebase = useContext(FirebaseContext); // Usar contexto de Firebase
    const [groupedDeliveries, setGroupedDeliveries] = useState([]);
    const [selectedDelivery, setSelectedDelivery] = useState(null);
    const [refreshing, setRefreshing] = useState(false);
    const [loading, setLoading] = useState(true);

    // Estados para el checklist de productos
    const [checklistVisible, setChecklistVisible] = useState(false);
    const [selectedOrder, setSelectedOrder] = useState(null);
    const [currentDeliveryId, setCurrentDeliveryId] = useState(null);

    const { updateBooking } = api;

    // Función auxiliar para convertir booking a formato de orden
    const convertBookingToOrder = (booking) => {
        return {
            customerId: booking.customer,
            bookingId: booking.id,
            customerName: booking.customer_name || 'Cliente',
            customerPhone: booking.customer_phone || booking.phone || '',
            deliveryAddress: {
                lat: booking.drop?.lat || 0,
                lng: booking.drop?.lng || 0,
                address: booking.drop?.add || booking.dropAddress || 'Dirección no disponible'
            },
            products: booking.products || [],
            status: booking.status === 'NEW' ? 'pending' :
                   booking.status === 'ACCEPTED' ? 'accepted' :
                   booking.status === 'ARRIVED' ? 'in_progress' :
                   booking.status === 'STARTED' ? 'in_progress' : 'pending'
        };
    };

    // Función auxiliar para agrupar bookings por entrega agrupada
    const groupBookingsByDelivery = (bookings) => {
        const groupedDeliveries = {};

        bookings.forEach(booking => {
            // Usar groupedDeliveryId, folio, o crear uno basado en fecha si no existe
            const groupId = booking.groupedDeliveryId ||
                           booking.folio ||
                           `GRP-${new Date(booking.bookingDate).toISOString().slice(0, 10)}`;

            if (!groupedDeliveries[groupId]) {
                groupedDeliveries[groupId] = {
                    id: groupId,
                    folio: booking.folio || `ENT-${new Date(booking.bookingDate).getFullYear()}-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
                    driver: booking.driver,
                    status: booking.status === 'NEW' ? 'assigned' :
                           booking.status === 'ACCEPTED' ? 'accepted' : 'assigned',
                    createdAt: booking.bookingDate || new Date().getTime(),
                    orders: []
                };
            }

            groupedDeliveries[groupId].orders.push(convertBookingToOrder(booking));
        });

        return Object.values(groupedDeliveries)
            .sort((a, b) => (b.createdAt || 0) - (a.createdAt || 0));
    };

    // Función de prueba para diagnosticar problemas
    const testFirebaseConnection = async () => {
        try {
            console.log('🧪 Iniciando prueba de conexión a Firebase...');
            
            if (!firebase) {
                console.error('❌ Firebase no está disponible (contexto)');
                return false;
            }
            
            if (!auth.profile?.uid) {
                console.error('❌ No hay ID de conductor');
                return false;
            }
            
            console.log('👤 ID del conductor:', auth.profile.uid);
            
            // Intentar conectar a Firebase
            const testRef = firebase.bookingListRef(auth.profile.uid, 'driver');
            console.log('📡 Referencia de Firebase creada');
            
            return new Promise((resolve) => {
                const timeout = setTimeout(() => {
                    console.error('⏰ Timeout en la conexión a Firebase');
                    resolve(false);
                }, 10000); // 10 segundos de timeout
                
                onValue(testRef, (snapshot) => {
                    clearTimeout(timeout);
                    console.log('✅ Conexión a Firebase exitosa');
                    console.log('📊 Datos obtenidos:', snapshot.val() ? 'SÍ' : 'NO');
                    resolve(true);
                }, (error) => {
                    clearTimeout(timeout);
                    console.error('❌ Error en conexión a Firebase:', error);
                    resolve(false);
                });
            });
            
        } catch (error) {
            console.error('❌ Error en prueba de conexión:', error);
            return false;
        }
    };

    useEffect(() => {
        // Verificar que Firebase esté disponible
        if (firebase) {
            console.log('✅ Firebase está disponible en el contexto');
            console.log('📊 Funciones disponibles:', Object.keys(firebase));
        loadGroupedDeliveries();
        } else {
            console.error('❌ Firebase no está disponible en el contexto');
            Alert.alert('Error', 'Firebase no está inicializado. Reinicia la aplicación.');
        }

        // Cleanup function para limpiar listeners de Firebase
        return () => {
            try {
                if (firebase && auth.profile?.uid) {
                    const bookingsRef = firebase.bookingListRef(auth.profile.uid, 'driver');
                off(bookingsRef);
                }
            } catch (error) {
                console.log('Error al limpiar listeners:', error);
            }
        };
    }, [firebase]); // Dependencia en firebase para que se ejecute cuando esté disponible

    // Función para probar conexión manualmente
    const handleTestConnection = async () => {
        const isConnected = await testFirebaseConnection();
        if (isConnected) {
            Alert.alert('✅ Conexión Exitosa', 'Firebase está funcionando correctamente');
        } else {
            Alert.alert('❌ Error de Conexión', 'No se pudo conectar a Firebase. Verifica tu conexión a internet.');
        }
    };

    const loadGroupedDeliveries = async () => {
        try {
            setLoading(true);
            console.log('🔄 Iniciando carga de entregas agrupadas...');
            Alert.alert('Debug', '🔄 Iniciando carga de entregas agrupadas...');

            // 🔄 Verificar que Firebase esté disponible
            if (!firebase) {
                console.error('❌ Firebase no está disponible (contexto)');
                Alert.alert('Error', 'Firebase no está inicializado. Reinicia la aplicación.');
                setLoading(false);
                return;
            }

            // 🔄 Verificar que bookingListRef esté disponible
            if (!firebase.bookingListRef) {
                console.error('❌ bookingListRef no está disponible');
                Alert.alert('Error', 'Función bookingListRef no está disponible.');
                setLoading(false);
                return;
            }

            // Verificar que tenemos el ID del conductor
            if (!auth.profile?.uid) {
                console.error('❌ No se encontró ID del conductor:', auth.profile);
                Alert.alert('Error', 'No se pudo identificar al conductor. Por favor, inicia sesión nuevamente.');
                setLoading(false);
                return;
            }

            console.log('👤 ID del conductor:', auth.profile.uid);
            Alert.alert('Debug', `👤 ID del conductor: ${auth.profile.uid}`);

            // Consultar bookings del conductor actual
            const bookingsRef = firebase.bookingListRef(auth.profile.uid, 'driver');
            console.log('📡 Conectando a Firebase...');
            Alert.alert('Debug', '📡 Conectando a Firebase...');

            // Limpiar listener anterior si existe
            off(bookingsRef);

            onValue(bookingsRef, (snapshot) => {
                console.log('📥 Respuesta de Firebase recibida');
                Alert.alert('Debug', '📥 Respuesta de Firebase recibida');
                
                if (snapshot.val()) {
                    const bookingsData = snapshot.val();
                    console.log('📊 Total bookings obtenidos:', Object.keys(bookingsData).length);
                    Alert.alert('Debug', `📊 Total bookings obtenidos: ${Object.keys(bookingsData).length}`);

                    // Filtrar bookings que tienen productos o son entregas agrupadas
                    const allBookings = Object.keys(bookingsData)
                        .map(id => ({ ...bookingsData[id], id }))
                        .filter(booking => {
                            // Incluir bookings con productos
                            const hasProducts = booking.products && booking.products.length > 0;
                            // Incluir bookings con estados activos (más flexibles)
                            const isActiveStatus = ['NEW', 'ACCEPTED', 'ARRIVED', 'STARTED', 'PENDING', 'ASSIGNED', 'COMPLETE'].includes(booking.status);
                            // Incluir bookings con folio (para agrupar)
                            const hasFolio = booking.folio;
                            // Incluir bookings con deliveryType específico
                            const isGroupedDelivery = booking.deliveryType === 'grouped_delivery';
                            // Incluir bookings que tengan driver asignado
                            const hasDriver = booking.driver;
                            
                            // Log detallado de cada booking
                            console.log(`📋 Booking ${booking.id}:`, {
                                hasProducts,
                                hasFolio,
                                isActiveStatus,
                                isGroupedDelivery,
                                hasDriver,
                                status: booking.status,
                                productsCount: booking.products?.length || 0,
                                folio: booking.folio,
                                deliveryType: booking.deliveryType,
                                customer: booking.customer,
                                driver: booking.driver
                            });
                            
                            // Mostrar alert con detalles del primer booking para debugging
                            if (booking.id === Object.keys(bookingsData)[0]) {
                                Alert.alert('Debug - Primer Booking', 
                                    `ID: ${booking.id}\n` +
                                    `Status: ${booking.status}\n` +
                                    `Folio: ${booking.folio || 'NO'}\n` +
                                    `Productos: ${booking.products?.length || 0}\n` +
                                    `DeliveryType: ${booking.deliveryType || 'NO'}\n` +
                                    `Driver: ${booking.driver || 'NO'}`
                                );
                            }
                            
                            // Filtro más flexible: incluir si tiene driver asignado y está en estado activo
                            return isActiveStatus && hasDriver;
                        });

                    console.log('📋 Bookings filtrados con productos/folio:', allBookings.length);
                    Alert.alert('Debug', `📋 Bookings filtrados: ${allBookings.length}`);

                    // Log adicional para mostrar tipos de bookings
                    const statusCounts = {};
                    allBookings.forEach(booking => {
                        statusCounts[booking.status] = (statusCounts[booking.status] || 0) + 1;
                    });
                    console.log('📊 Distribución por status:', statusCounts);
                    Alert.alert('Debug - Distribución', 
                        `Status de bookings:\n` +
                        Object.entries(statusCounts).map(([status, count]) => `${status}: ${count}`).join('\n')
                    );

                    if (allBookings.length === 0) {
                        console.log('⚠️ No se encontraron bookings válidos para agrupar');
                        Alert.alert('Debug', '⚠️ No se encontraron bookings válidos para agrupar');
                        setGroupedDeliveries([]);
                        setLoading(false);
                        setRefreshing(false);
                        return;
                    }

                    // Agrupar bookings por folio
                    const groupedByFolio = {};
                    
                    allBookings.forEach(booking => {
                        const folio = booking.folio || `SIN-FOLIO-${booking.id}`;
                        
                        if (!groupedByFolio[folio]) {
                            groupedByFolio[folio] = {
                                folio: folio,
                                driver: booking.driver,
                                status: booking.status,
                                createdAt: booking.bookingDate || new Date().getTime(),
                                orders: []
                            };
                        }
                        
                        // Convertir booking a formato de orden
                        const order = {
                            customerId: booking.customer,
                            bookingId: booking.id,
                            customerName: booking.customer_name || 'Cliente',
                            customerPhone: booking.customer_phone || booking.phone || '',
                            deliveryAddress: {
                                lat: booking.drop?.lat || 0,
                                lng: booking.drop?.lng || 0,
                                address: booking.drop?.add || booking.dropAddress || 'Dirección no disponible'
                            },
                            pickupAddress: {
                                lat: booking.pickup?.lat || 0,
                                lng: booking.pickup?.lng || 0,
                                address: booking.pickup?.add || booking.pickupAddress || 'Dirección no disponible'
                            },
                            products: booking.products || [
                                // Agregar productos de prueba para el primer booking
                                ...(booking.id === Object.keys(bookingsData)[0] ? [
                                    {
                                        id: "PROD-001",
                                        name: "Laptop Dell Inspiron",
                                        sku: "LAP-DELL-001",
                                        quantity: 1,
                                        description: "Laptop para oficina con Windows 11",
                                        status: "PENDING",
                                        price: 25000
                                    },
                                    {
                                        id: "PROD-002", 
                                        name: "Mouse Inalámbrico",
                                        sku: "MOUSE-WIRELESS-001",
                                        quantity: 2,
                                        description: "Mouse inalámbrico ergonómico",
                                        status: "PENDING",
                                        price: 500
                                    }
                                ] : []),
                                // Agregar productos de prueba para el segundo booking
                                ...(booking.id === Object.keys(bookingsData)[1] ? [
                                    {
                                        id: "PROD-003",
                                        name: "Monitor Samsung 24\"",
                                        sku: "MON-SAMSUNG-001", 
                                        quantity: 1,
                                        description: "Monitor LED Full HD",
                                        status: "PENDING",
                                        price: 3500
                                    },
                                    {
                                        id: "PROD-004",
                                        name: "Teclado Mecánico",
                                        sku: "KEYBOARD-MECH-001",
                                        quantity: 1,
                                        description: "Teclado mecánico RGB",
                                        status: "PENDING", 
                                        price: 1200
                                    },
                                    {
                                        id: "PROD-005",
                                        name: "Cable HDMI",
                                        sku: "CABLE-HDMI-001",
                                        quantity: 3,
                                        description: "Cable HDMI 2.0 de alta velocidad",
                                        status: "PENDING",
                                        price: 150
                                    }
                                ] : [])
                            ],
                            status: booking.status === 'NEW' ? 'pending' :
                                   booking.status === 'ACCEPTED' ? 'accepted' :
                                   booking.status === 'ARRIVED' ? 'in_progress' :
                                   booking.status === 'STARTED' ? 'in_progress' : 'pending',
                            estimate: booking.estimate || '0',
                            distance: booking.distance || '0'
                        };
                        
                        groupedByFolio[folio].orders.push(order);
                    });

                    // Convertir a array y ordenar por fecha
                    const deliveriesArray = Object.values(groupedByFolio)
                        .filter(delivery => delivery.orders.length > 0) // Solo entregas con órdenes
                        .sort((a, b) => (b.createdAt || 0) - (a.createdAt || 0));

                    console.log('🚚 Entregas agrupadas procesadas:', deliveriesArray.length);
                    Alert.alert('Debug', `🚚 Entregas agrupadas procesadas: ${deliveriesArray.length}`);
                    
                    // Log de ejemplo para debugging
                    if (deliveriesArray.length > 0) {
                        console.log('📄 Ejemplo de entrega agrupada:', {
                            folio: deliveriesArray[0].folio,
                            ordersCount: deliveriesArray[0].orders.length,
                            totalProducts: deliveriesArray[0].orders.reduce((total, order) => 
                                total + (order.products?.length || 0), 0
                            )
                        });
                        
                        // Log detallado de productos por entrega
                        deliveriesArray.forEach((delivery, index) => {
                            const totalProducts = delivery.orders.reduce((total, order) => 
                                total + (order.products?.length || 0), 0
                            );
                            console.log(`📦 Entrega ${index + 1} (${delivery.folio}): ${totalProducts} productos`);
                            
                            delivery.orders.forEach((order, orderIndex) => {
                                if (order.products && order.products.length > 0) {
                                    console.log(`  📋 Orden ${orderIndex + 1}: ${order.products.length} productos`);
                                    order.products.forEach(product => {
                                        console.log(`    - ${product.name} (${product.quantity}x) - $${product.price}`);
                                    });
                                }
                            });
                        });
                        
                        Alert.alert('Debug', `📄 Ejemplo: ${deliveriesArray[0].folio} con ${deliveriesArray[0].orders.length} órdenes y ${deliveriesArray[0].orders.reduce((total, order) => total + (order.products?.length || 0), 0)} productos`);
                    }
                    
                    setGroupedDeliveries(deliveriesArray);

                } else {
                    console.log('📭 No se encontraron bookings para el conductor');
                    Alert.alert('Debug', '📭 No se encontraron bookings para el conductor');
                    setGroupedDeliveries([]);
                }

                setLoading(false);
                setRefreshing(false);
            }, (error) => {
                // Manejar errores específicos de Firebase
                console.error('❌ Error específico de Firebase:', error);
                Alert.alert('Error de Conexión', 'No se pudo conectar a Firebase. Verifica tu conexión a internet.');
                setLoading(false);
                setRefreshing(false);
            });

        } catch (error) {
            console.error('❌ Error al cargar entregas agrupadas:', error);
            console.error('❌ Detalles del error:', {
                message: error.message,
                code: error.code,
                stack: error.stack
            });
            Alert.alert('Error', `No se pudieron cargar las entregas agrupadas: ${error.message}`);
            setLoading(false);
            setRefreshing(false);
        }
    };

    const onRefresh = () => {
        setRefreshing(true);
        loadGroupedDeliveries();
    };

    const acceptGroupedDelivery = (delivery) => {
        Alert.alert(
            'Aceptar Entrega Agrupada',
            `¿Deseas aceptar la entrega con folio ${delivery.folio}?`,
            [
                { text: 'Cancelar', style: 'cancel' },
                { 
                    text: 'Aceptar', 
                    onPress: () => {
                        // Actualizar estado de la entrega
                        setGroupedDeliveries(prev => 
                            prev.map(d => 
                                d.id === delivery.id 
                                    ? { ...d, status: 'accepted' }
                                    : d
                            )
                        );
                        Alert.alert('Éxito', 'Entrega agrupada aceptada');
                    }
                }
            ]
        );
    };

    const startGroupedDelivery = (delivery) => {
        setSelectedDelivery(delivery);
    };

    // Funciones para manejar el checklist de productos
    const openProductChecklist = (order, deliveryId) => {
        setSelectedOrder(order);
        setCurrentDeliveryId(deliveryId);
        setChecklistVisible(true);
    };

    const closeProductChecklist = () => {
        setChecklistVisible(false);
        setSelectedOrder(null);
        setCurrentDeliveryId(null);
    };

    const updateOrderProducts = async (customerId, updatedProducts) => {
        // 1. Actualizar estado local (interfaz)
        setGroupedDeliveries(prevDeliveries => {
            return prevDeliveries.map(delivery => {
                if (delivery.id === currentDeliveryId) {
                    const updatedOrders = delivery.orders.map(order => {
                        if (order.customerId === customerId) {
                            return {
                                ...order,
                                products: updatedProducts
                            };
                        }
                        return order;
                    });
                    return { ...delivery, orders: updatedOrders };
                }
                return delivery;
            });
        });

        // 2. NUEVO: Guardar en Firebase/Base de datos
        try {
            // Encontrar el booking correspondiente
            const currentDelivery = groupedDeliveries.find(d => d.id === currentDeliveryId);
            const currentOrder = currentDelivery?.orders.find(o => o.customerId === customerId);

            if (currentOrder && currentOrder.bookingId) {
                // Crear objeto de booking actualizado
                const updatedBooking = {
                    id: currentOrder.bookingId,
                    products: updatedProducts,
                    productChecklistUpdated: true,
                    productChecklistTimestamp: new Date().getTime(),
                    groupedDeliveryId: currentDeliveryId,
                    customerId: customerId
                };

                console.log('🔄 Guardando productos en Firebase:', updatedBooking);

                // Guardar en Firebase usando updateBooking
                dispatch(updateBooking(updatedBooking));

                console.log('✅ Productos guardados exitosamente en la base de datos');
            } else {
                console.warn('⚠️ No se encontró bookingId para guardar los productos');
            }
        } catch (error) {
            console.error('❌ Error al guardar productos en base de datos:', error);
            Alert.alert(
                'Error de Conexión',
                'No se pudieron guardar los cambios en la base de datos. Los cambios se mantienen localmente.',
                [{ text: 'OK' }]
            );
        }
    };

    const handleCompleteOrder = (orderIndex, deliveryData) => {
        // Actualizar el estado de la orden específica
        setSelectedDelivery(prev => {
            const updatedOrders = [...prev.orders];
            updatedOrders[orderIndex] = {
                ...updatedOrders[orderIndex],
                status: deliveryData.verification.partialDelivery ? 'partial' : 'completed',
                deliveryVerification: deliveryData
            };
            return { ...prev, orders: updatedOrders };
        });

        // Verificar si todas las órdenes están completadas
        const allCompleted = selectedDelivery.orders.every((order, index) => {
            if (index === orderIndex) {
                return true; // La orden actual se acaba de completar
            }
            return order.status === 'completed' || order.status === 'partial';
        });

        if (allCompleted) {
            Alert.alert(
                'Entrega Agrupada Completada',
                'Todas las entregas han sido completadas exitosamente',
                [
                    {
                        text: 'OK',
                        onPress: () => {
                            setSelectedDelivery(null);
                            loadGroupedDeliveries(); // Recargar la lista
                        }
                    }
                ]
            );
        }
    };

    const getDeliveryStatusColor = (status) => {
        switch (status) {
            case 'completed': return colors.GREEN;
            case 'accepted': return colors.BLUE;
            case 'assigned': return colors.ORANGE;
            default: return colors.GREY;
        }
    };

    const getDeliveryStatusText = (status) => {
        switch (status) {
            case 'completed': return 'Completada';
            case 'accepted': return 'Aceptada';
            case 'assigned': return 'Asignada';
            default: return 'Pendiente';
        }
    };

    const renderDeliveryCard = (delivery) => {
        const statusColor = getDeliveryStatusColor(delivery.status);
        const statusText = getDeliveryStatusText(delivery.status);
        const totalProducts = delivery.orders.reduce((sum, order) => sum + (order.products?.length || 0), 0);
        const deliveredProducts = delivery.orders.reduce((sum, order) => 
            sum + (order.products?.filter(p => p.delivered).length || 0), 0
        );
        const progressPercentage = totalProducts > 0 ? (deliveredProducts / totalProducts) * 100 : 0;

        return (
            <Card key={delivery.folio} containerStyle={styles.deliveryCard}>
                <View style={[styles.cardHeader, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                    <View style={styles.deliveryInfo}>
                        <Text style={styles.folioText}>📋 Folio: {delivery.folio}</Text>
                        <Text style={styles.ordersCount}>🚚 {delivery.orders.length} entregas</Text>
                        <Text style={styles.productsCount}>📦 {totalProducts} productos total</Text>
                        <Text style={styles.progressText}>
                            ✅ {deliveredProducts} entregados ({Math.round(progressPercentage)}%)
                        </Text>
                    </View>
                    
                    <View style={styles.statusContainer}>
                        <View style={[styles.statusBadge, { backgroundColor: statusColor }]}>
                            <Text style={styles.statusText}>{statusText}</Text>
                        </View>
                    </View>
                </View>

                <View style={styles.ordersPreview}>
                    {delivery.orders.slice(0, 3).map((order, index) => {
                        const orderDeliveredProducts = order.products?.filter(p => p.delivered).length || 0;
                        const orderTotalProducts = order.products?.length || 0;
                        const orderProgressPercentage = orderTotalProducts > 0 ? (orderDeliveredProducts / orderTotalProducts) * 100 : 0;

                        return (
                            <View key={index} style={styles.orderRow}>
                                <View style={styles.orderInfo}>
                                    <Text style={styles.customerName} numberOfLines={1}>
                                        👤 {order.customerName}
                                    </Text>
                                    <Text style={styles.orderAddress} numberOfLines={1}>
                                        📍 {order.deliveryAddress.address}
                                    </Text>
                                    <Text style={styles.productProgress}>
                                        📦 {orderDeliveredProducts}/{orderTotalProducts} productos ({Math.round(orderProgressPercentage)}%)
                                    </Text>
                                </View>

                                {(delivery.status === 'accepted' || delivery.status === 'in_progress') && (
                                    <TouchableOpacity
                                        style={styles.checklistButton}
                                        onPress={() => openProductChecklist(order, delivery.folio)}
                                    >
                                        <Icon
                                            name="checklist"
                                            type="material"
                                            size={20}
                                            color={colors.BLUE}
                                        />
                                        <Text style={styles.checklistButtonText}>Checklist</Text>
                                    </TouchableOpacity>
                                )}
                            </View>
                        );
                    })}
                    
                    {delivery.orders.length > 3 && (
                        <View style={styles.moreOrders}>
                            <Text style={styles.moreOrdersText}>
                                ... y {delivery.orders.length - 3} entregas más
                            </Text>
                        </View>
                    )}
                </View>

                <View style={styles.cardActions}>
                    {delivery.status === 'assigned' && (
                        <Button
                            title="Aceptar Entrega"
                            onPress={() => acceptGroupedDelivery(delivery)}
                            buttonStyle={[styles.actionButton, { backgroundColor: colors.GREEN }]}
                            titleStyle={styles.actionButtonText}
                        />
                    )}
                    
                    {delivery.status === 'accepted' && (
                        <Button
                            title="Iniciar Entregas"
                            onPress={() => startGroupedDelivery(delivery)}
                            buttonStyle={[styles.actionButton, { backgroundColor: colors.BLUE }]}
                            titleStyle={styles.actionButtonText}
                        />
                    )}

                    <TouchableOpacity
                        style={styles.viewDetailsButton}
                        onPress={() => setSelectedDelivery(delivery)}
                    >
                        <Text style={styles.viewDetailsText}>Ver Detalles</Text>
                        <Icon name="arrow-forward" type="material" size={16} color={colors.BLUE} />
                    </TouchableOpacity>
                </View>
            </Card>
        );
    };

    if (selectedDelivery) {
        // Log para debugging - verificar qué datos se envían
        console.log('📤 Enviando datos a GroupedDeliveryDetails:', {
            folio: selectedDelivery.folio,
            ordersCount: selectedDelivery.orders.length,
            ordersWithProducts: selectedDelivery.orders.filter(order => order.products && order.products.length > 0).length,
            totalProducts: selectedDelivery.orders.reduce((total, order) => total + (order.products?.length || 0), 0)
        });
        
        // Alert para debugging
        Alert.alert('Debug - GroupedDeliveryScreen', 
            `Enviando a GroupedDeliveryDetails:\n` +
            `Folio: ${selectedDelivery.folio}\n` +
            `Órdenes: ${selectedDelivery.orders.length}\n` +
            `Órdenes con productos: ${selectedDelivery.orders.filter(order => order.products && order.products.length > 0).length}\n` +
            `Total productos: ${selectedDelivery.orders.reduce((total, order) => total + (order.products?.length || 0), 0)}`
        );
        
        return (
            <View style={styles.container}>
                <View style={styles.header}>
                    <TouchableOpacity 
                        onPress={() => setSelectedDelivery(null)}
                        style={styles.backButton}
                    >
                        <Icon
                            name={isRTL ? "arrow-forward" : "arrow-back"}
                            type="material"
                            color={colors.WHITE}
                            size={24}
                        />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>Entrega Agrupada</Text>
                </View>
                
                <GroupedDeliveryDetails
                    deliveryData={selectedDelivery}
                    onCompleteOrder={handleCompleteOrder}
                />
            </View>
        );
    }

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity 
                    onPress={() => props.navigation.goBack()}
                    style={styles.backButton}
                >
                    <Icon
                        name={isRTL ? "arrow-forward" : "arrow-back"}
                        type="material"
                        color={colors.WHITE}
                        size={24}
                    />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>Entregas Agrupadas</Text>
            </View>

            <ScrollView
                style={styles.content}
                refreshControl={
                    <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
                }
            >
                {loading ? (
                    <View style={styles.loadingContainer}>
                        <Text style={styles.loadingText}>Cargando entregas...</Text>
                    </View>
                ) : groupedDeliveries.length === 0 ? (
                    <View style={styles.emptyContainer}>
                        <Icon name="local-shipping" type="material" size={64} color={colors.GREY} />
                        <Text style={styles.emptyText}>No hay entregas agrupadas disponibles</Text>
                        
                        {/* Botón de prueba para diagnosticar */}
                        <TouchableOpacity
                            style={styles.testButton}
                            onPress={handleTestConnection}
                        >
                            <Text style={styles.testButtonText}>🔧 Probar Conexión</Text>
                        </TouchableOpacity>
                        
                        <TouchableOpacity
                            style={styles.testButton}
                            onPress={loadGroupedDeliveries}
                        >
                            <Text style={styles.testButtonText}>🔄 Recargar Datos</Text>
                        </TouchableOpacity>
                    </View>
                ) : (
                    groupedDeliveries.map(delivery => renderDeliveryCard(delivery))
                )}
            </ScrollView>

            {/* Modal de checklist de productos */}
            <ProductChecklist
                visible={checklistVisible}
                onClose={closeProductChecklist}
                order={selectedOrder}
                onUpdateProducts={updateOrderProducts}
                deliveryId={currentDeliveryId}
            />
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.BACKGROUND,
    },
    header: {
        backgroundColor: colors.HEADER,
        paddingTop: 50,
        paddingBottom: 15,
        paddingHorizontal: 20,
        flexDirection: 'row',
        alignItems: 'center',
        elevation: 3,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    backButton: {
        marginRight: 15,
        padding: 5,
    },
    headerTitle: {
        fontSize: 20,
        fontFamily: fonts.Bold,
        color: colors.WHITE,
        flex: 1,
    },
    content: {
        flex: 1,
        padding: 15,
    },
    deliveryCard: {
        borderRadius: 10,
        marginBottom: 15,
        elevation: 3,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
    },
    cardHeader: {
        alignItems: 'center',
        marginBottom: 15,
    },
    deliveryInfo: {
        flex: 1,
    },
    folioText: {
        fontSize: 16,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginBottom: 3,
    },
    ordersCount: {
        fontSize: 14,
        fontFamily: fonts.Regular,
        color: colors.BLUE,
        marginBottom: 2,
    },
    productsCount: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GREY,
    },
    progressText: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.BLUE,
        marginTop: 2,
    },
    statusContainer: {
        alignItems: 'flex-end',
    },
    statusBadge: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 15,
    },
    statusText: {
        fontSize: 12,
        fontFamily: fonts.Bold,
        color: colors.WHITE,
    },
    ordersPreview: {
        marginBottom: 15,
    },
    orderRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 8,
        paddingVertical: 4,
    },
    orderInfo: {
        flex: 1,
        marginRight: 8,
    },
    customerName: {
        fontSize: 13,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginBottom: 1,
    },
    orderAddress: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        marginBottom: 1,
    },
    productProgress: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.BLUE,
        marginTop: 2,
        marginLeft: 10,
    },
    moreOrders: {
        alignItems: 'center',
        marginTop: 8,
    },
    moreOrdersText: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.BLUE,
        fontStyle: 'italic',
    },
    checklistButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: colors.WHITE,
        borderWidth: 1,
        borderColor: colors.BLUE,
        borderRadius: 6,
        paddingHorizontal: 8,
        paddingVertical: 4,
    },
    checklistButtonText: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.BLUE,
        marginLeft: 4,
    },
    cardActions: {
        marginTop: 10,
    },
    actionButton: {
        borderRadius: 8,
        paddingVertical: 12,
    },
    actionButtonText: {
        fontSize: 14,
        fontFamily: fonts.Bold,
        color: colors.WHITE,
    },
    viewDetailsButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 10,
        paddingVertical: 10,
        backgroundColor: colors.WHITE,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: colors.BLUE,
    },
    viewDetailsText: {
        fontSize: 14,
        fontFamily: fonts.Bold,
        color: colors.BLUE,
        marginRight: 5,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingTop: 50,
    },
    loadingText: {
        fontSize: 16,
        fontFamily: fonts.Regular,
        color: colors.GREY,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingTop: 100,
    },
    emptyText: {
        fontSize: 16,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        textAlign: 'center',
        marginTop: 20,
    },
    testButton: {
        marginTop: 20,
        paddingVertical: 10,
        paddingHorizontal: 20,
        backgroundColor: colors.BLUE,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: colors.WHITE,
    },
    testButtonText: {
        fontSize: 14,
        fontFamily: fonts.Bold,
        color: colors.WHITE,
    },
});
