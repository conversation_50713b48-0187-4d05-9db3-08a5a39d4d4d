/**
 * Aplicación de demostración que muestra las funcionalidades implementadas
 * Esta app funciona con Expo Go y demuestra el uso de las APIs implementadas
 */

import React, { useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    TextInput,
    Alert,
    ActivityIndicator,
    SafeAreaView
} from 'react-native';

// Simulamos las funciones API (en la app real estarían en sharedFunctions.js)
const API_BASE_URL = 'https://us-central1-balle-813e3.cloudfunctions.net';

const apiRequest = async (endpoint, options = {}) => {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    };

    const requestOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };

    try {
        const response = await fetch(url, requestOptions);
        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || `HTTP error! status: ${response.status}`);
        }

        return data;
    } catch (error) {
        console.error(`Error en ${endpoint}:`, error);
        throw error;
    }
};

const createCustomer = async (customerData) => {
    return apiRequest('/createUser', {
        method: 'POST',
        body: JSON.stringify(customerData)
    });
};

const createDriver = async (driverData) => {
    return apiRequest('/createDriver', {
        method: 'POST',
        body: JSON.stringify(driverData)
    });
};

const createVehicle = async (vehicleData) => {
    return apiRequest('/createVehicle', {
        method: 'POST',
        body: JSON.stringify(vehicleData)
    });
};

const createGroupedDelivery = async (deliveryData) => {
    return apiRequest('/receiveGroupedDelivery', {
        method: 'POST',
        body: JSON.stringify(deliveryData)
    });
};

export default function DemoApp() {
    const [activeTab, setActiveTab] = useState('customer');
    const [loading, setLoading] = useState(false);
    
    // Estados para formularios
    const [customerForm, setCustomerForm] = useState({
        firstName: 'Juan',
        lastName: 'Pérez',
        email: '<EMAIL>',
        mobile: '+************',
        password: 'MiPassword123',
        nombreComercial: 'Empresa Juan S.A.',
        rfc: 'XAXX010101000',
        city: 'Guadalajara'
    });

    const [driverForm, setDriverForm] = useState({
        firstName: 'Carlos',
        lastName: 'Rodríguez',
        email: '<EMAIL>',
        mobile: '+523334567893',
        password: 'DriverPass123'
    });

    const [vehicleForm, setVehicleForm] = useState({
        vehicleNumber: 'ABC123456',
        vehicleMake: 'Ford',
        vehicleModel: 'F-150',
        carType: 'CAMIONETA',
        vehicleColor: 'Blanco',
        vehicleYear: '2024',
        stateLicensePlate: 'XYZ2025'
    });

    const handleCreateCustomer = async () => {
        setLoading(true);
        try {
            const result = await createCustomer(customerForm);
            Alert.alert('Éxito', 'Cliente creado exitosamente');
            console.log('Cliente creado:', result);
        } catch (error) {
            Alert.alert('Error', error.message);
        } finally {
            setLoading(false);
        }
    };

    const handleCreateDriver = async () => {
        setLoading(true);
        try {
            const result = await createDriver(driverForm);
            Alert.alert('Éxito', 'Conductor creado exitosamente');
            console.log('Conductor creado:', result);
        } catch (error) {
            Alert.alert('Error', error.message);
        } finally {
            setLoading(false);
        }
    };

    const handleCreateVehicle = async () => {
        setLoading(true);
        try {
            const result = await createVehicle(vehicleForm);
            Alert.alert('Éxito', 'Vehículo creado exitosamente');
            console.log('Vehículo creado:', result);
        } catch (error) {
            Alert.alert('Error', error.message);
        } finally {
            setLoading(false);
        }
    };

    const handleCreateDelivery = async () => {
        setLoading(true);
        try {
            const deliveryData = {
                folio: 'ENT-2024-001',
                driver: 'driver_uid_123',
                vehicle: {
                    type: 'CAMIONETA',
                    plate: 'ABC-123'
                },
                orders: [{
                    customerId: 'customer_uid_456',
                    customerName: 'María García',
                    customerEmail: '<EMAIL>',
                    customerPhone: '+************',
                    pickupAddress: {
                        lat: 19.4326,
                        lng: -99.1332,
                        address: 'Almacén Central'
                    },
                    deliveryAddress: {
                        lat: 19.4327,
                        lng: -99.1333,
                        address: 'Destino Final'
                    },
                    products: [{
                        id: 'PROD-001',
                        name: 'Producto de prueba',
                        quantity: 2,
                        sku: 'SKU-001'
                    }]
                }]
            };

            const result = await createGroupedDelivery(deliveryData);
            Alert.alert('Éxito', 'Entrega agrupada creada exitosamente');
            console.log('Entrega creada:', result);
        } catch (error) {
            Alert.alert('Error', error.message);
        } finally {
            setLoading(false);
        }
    };

    const renderCustomerForm = () => (
        <View style={styles.formContainer}>
            <Text style={styles.formTitle}>Crear Cliente</Text>
            <TextInput
                style={styles.input}
                placeholder="Nombre"
                value={customerForm.firstName}
                onChangeText={(text) => setCustomerForm({...customerForm, firstName: text})}
            />
            <TextInput
                style={styles.input}
                placeholder="Apellido"
                value={customerForm.lastName}
                onChangeText={(text) => setCustomerForm({...customerForm, lastName: text})}
            />
            <TextInput
                style={styles.input}
                placeholder="Email"
                value={customerForm.email}
                onChangeText={(text) => setCustomerForm({...customerForm, email: text})}
                keyboardType="email-address"
            />
            <TextInput
                style={styles.input}
                placeholder="Teléfono"
                value={customerForm.mobile}
                onChangeText={(text) => setCustomerForm({...customerForm, mobile: text})}
                keyboardType="phone-pad"
            />
            <TouchableOpacity 
                style={[styles.button, loading && styles.buttonDisabled]} 
                onPress={handleCreateCustomer}
                disabled={loading}
            >
                {loading ? (
                    <ActivityIndicator color="#fff" />
                ) : (
                    <Text style={styles.buttonText}>Crear Cliente</Text>
                )}
            </TouchableOpacity>
        </View>
    );

    const renderDriverForm = () => (
        <View style={styles.formContainer}>
            <Text style={styles.formTitle}>Crear Conductor</Text>
            <TextInput
                style={styles.input}
                placeholder="Nombre"
                value={driverForm.firstName}
                onChangeText={(text) => setDriverForm({...driverForm, firstName: text})}
            />
            <TextInput
                style={styles.input}
                placeholder="Apellido"
                value={driverForm.lastName}
                onChangeText={(text) => setDriverForm({...driverForm, lastName: text})}
            />
            <TextInput
                style={styles.input}
                placeholder="Email"
                value={driverForm.email}
                onChangeText={(text) => setDriverForm({...driverForm, email: text})}
                keyboardType="email-address"
            />
            <TextInput
                style={styles.input}
                placeholder="Teléfono"
                value={driverForm.mobile}
                onChangeText={(text) => setDriverForm({...driverForm, mobile: text})}
                keyboardType="phone-pad"
            />
            <TouchableOpacity 
                style={[styles.button, loading && styles.buttonDisabled]} 
                onPress={handleCreateDriver}
                disabled={loading}
            >
                {loading ? (
                    <ActivityIndicator color="#fff" />
                ) : (
                    <Text style={styles.buttonText}>Crear Conductor</Text>
                )}
            </TouchableOpacity>
        </View>
    );

    const renderVehicleForm = () => (
        <View style={styles.formContainer}>
            <Text style={styles.formTitle}>Crear Vehículo</Text>
            <TextInput
                style={styles.input}
                placeholder="Número de Vehículo"
                value={vehicleForm.vehicleNumber}
                onChangeText={(text) => setVehicleForm({...vehicleForm, vehicleNumber: text})}
            />
            <TextInput
                style={styles.input}
                placeholder="Marca"
                value={vehicleForm.vehicleMake}
                onChangeText={(text) => setVehicleForm({...vehicleForm, vehicleMake: text})}
            />
            <TextInput
                style={styles.input}
                placeholder="Modelo"
                value={vehicleForm.vehicleModel}
                onChangeText={(text) => setVehicleForm({...vehicleForm, vehicleModel: text})}
            />
            <TextInput
                style={styles.input}
                placeholder="Color"
                value={vehicleForm.vehicleColor}
                onChangeText={(text) => setVehicleForm({...vehicleForm, vehicleColor: text})}
            />
            <TouchableOpacity 
                style={[styles.button, loading && styles.buttonDisabled]} 
                onPress={handleCreateVehicle}
                disabled={loading}
            >
                {loading ? (
                    <ActivityIndicator color="#fff" />
                ) : (
                    <Text style={styles.buttonText}>Crear Vehículo</Text>
                )}
            </TouchableOpacity>
        </View>
    );

    const renderDeliveryDemo = () => (
        <View style={styles.formContainer}>
            <Text style={styles.formTitle}>Crear Entrega Agrupada</Text>
            <Text style={styles.description}>
                Esta demostración creará una entrega agrupada con datos de ejemplo.
            </Text>
            <TouchableOpacity 
                style={[styles.button, loading && styles.buttonDisabled]} 
                onPress={handleCreateDelivery}
                disabled={loading}
            >
                {loading ? (
                    <ActivityIndicator color="#fff" />
                ) : (
                    <Text style={styles.buttonText}>Crear Entrega de Prueba</Text>
                )}
            </TouchableOpacity>
        </View>
    );

    return (
        <SafeAreaView style={styles.container}>
            <Text style={styles.title}>Demo APIs Postman</Text>
            <Text style={styles.subtitle}>Aplicación de demostración</Text>
            
            {/* Tabs */}
            <View style={styles.tabContainer}>
                <TouchableOpacity 
                    style={[styles.tab, activeTab === 'customer' && styles.activeTab]}
                    onPress={() => setActiveTab('customer')}
                >
                    <Text style={[styles.tabText, activeTab === 'customer' && styles.activeTabText]}>
                        Cliente
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity 
                    style={[styles.tab, activeTab === 'driver' && styles.activeTab]}
                    onPress={() => setActiveTab('driver')}
                >
                    <Text style={[styles.tabText, activeTab === 'driver' && styles.activeTabText]}>
                        Conductor
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity 
                    style={[styles.tab, activeTab === 'vehicle' && styles.activeTab]}
                    onPress={() => setActiveTab('vehicle')}
                >
                    <Text style={[styles.tabText, activeTab === 'vehicle' && styles.activeTabText]}>
                        Vehículo
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity 
                    style={[styles.tab, activeTab === 'delivery' && styles.activeTab]}
                    onPress={() => setActiveTab('delivery')}
                >
                    <Text style={[styles.tabText, activeTab === 'delivery' && styles.activeTabText]}>
                        Entrega
                    </Text>
                </TouchableOpacity>
            </View>

            {/* Content */}
            <ScrollView style={styles.content}>
                {activeTab === 'customer' && renderCustomerForm()}
                {activeTab === 'driver' && renderDriverForm()}
                {activeTab === 'vehicle' && renderVehicleForm()}
                {activeTab === 'delivery' && renderDeliveryDemo()}
            </ScrollView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        textAlign: 'center',
        marginTop: 20,
        marginBottom: 5,
        color: '#333',
    },
    subtitle: {
        fontSize: 16,
        textAlign: 'center',
        marginBottom: 20,
        color: '#666',
    },
    tabContainer: {
        flexDirection: 'row',
        backgroundColor: '#fff',
        marginHorizontal: 10,
        borderRadius: 10,
        padding: 5,
    },
    tab: {
        flex: 1,
        paddingVertical: 10,
        alignItems: 'center',
        borderRadius: 8,
    },
    activeTab: {
        backgroundColor: '#007AFF',
    },
    tabText: {
        fontSize: 14,
        color: '#666',
    },
    activeTabText: {
        color: '#fff',
        fontWeight: 'bold',
    },
    content: {
        flex: 1,
        padding: 20,
    },
    formContainer: {
        backgroundColor: '#fff',
        borderRadius: 10,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },
    formTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 20,
        textAlign: 'center',
        color: '#333',
    },
    input: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 12,
        marginBottom: 15,
        fontSize: 16,
    },
    button: {
        backgroundColor: '#007AFF',
        borderRadius: 8,
        padding: 15,
        alignItems: 'center',
        marginTop: 10,
    },
    buttonDisabled: {
        backgroundColor: '#ccc',
    },
    buttonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
    },
    description: {
        fontSize: 14,
        color: '#666',
        textAlign: 'center',
        marginBottom: 20,
        lineHeight: 20,
    },
});
