/**
 * Componente para crear y actualizar vehículos usando los nuevos servicios API
 */

import React, { useState, useEffect } from 'react';
import {
    Card,
    CardContent,
    TextField,
    Button,
    Grid,
    Typography,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Alert,
    CircularProgress,
    Box,
    Switch,
    FormControlLabel
} from '@mui/material';
import { useVehicleService, useNotification } from '../hooks/useApiService';

const VehicleFormWithApi = ({ vehicleData = null, onSuccess, onCancel }) => {
    const [formData, setFormData] = useState({
        vehicleNumber: '',
        vehicleMake: '',
        vehicleModel: '',
        axles: 2,
        capacity: 0,
        carType: '',
        vehicleColor: '',
        vehicleYear: '',
        stateLicensePlate: '',
        // Campos adicionales para actualización
        vehicleId: '',
        car_image: '',
        other_info: '',
        active: true,
        approved: false,
        driver: '',
        unitNumber: '',
        vehicleCategory: '',
        federalLicensePlate: '',
        vehicleClass: '',
        vehicleType: '',
        vehicleNotes: ''
    });

    const [isEditMode, setIsEditMode] = useState(false);
    
    // Hooks para servicios API
    const { createVehicle, updateVehicle } = useVehicleService();
    const { notification, showSuccess, showError, clearNotification } = useNotification();

    useEffect(() => {
        if (vehicleData) {
            setFormData({
                vehicleNumber: vehicleData.vehicleNumber || '',
                vehicleMake: vehicleData.vehicleMake || '',
                vehicleModel: vehicleData.vehicleModel || '',
                axles: vehicleData.axles || 2,
                capacity: vehicleData.capacity || 0,
                carType: vehicleData.carType || '',
                vehicleColor: vehicleData.vehicleColor || '',
                vehicleYear: vehicleData.vehicleYear || '',
                stateLicensePlate: vehicleData.stateLicensePlate || '',
                // Campos adicionales
                vehicleId: vehicleData.vehicleId || vehicleData.id || '',
                car_image: vehicleData.car_image || '',
                other_info: vehicleData.other_info || '',
                active: vehicleData.active !== undefined ? vehicleData.active : true,
                approved: vehicleData.approved !== undefined ? vehicleData.approved : false,
                driver: vehicleData.driver || '',
                unitNumber: vehicleData.unitNumber || '',
                vehicleCategory: vehicleData.vehicleCategory || '',
                federalLicensePlate: vehicleData.federalLicensePlate || '',
                vehicleClass: vehicleData.vehicleClass || '',
                vehicleType: vehicleData.vehicleType || '',
                vehicleNotes: vehicleData.vehicleNotes || ''
            });
            setIsEditMode(true);
        }
    }, [vehicleData]);

    const handleInputChange = (field) => (event) => {
        const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const validateForm = () => {
        const requiredFields = ['vehicleNumber', 'vehicleMake', 'vehicleModel', 'carType', 'vehicleColor', 'vehicleYear', 'stateLicensePlate'];

        for (const field of requiredFields) {
            if (!formData[field].toString().trim()) {
                showError(`El campo ${field} es requerido`);
                return false;
            }
        }

        // Validar año del vehículo
        const currentYear = new Date().getFullYear();
        const vehicleYear = parseInt(formData.vehicleYear);
        if (vehicleYear < 1900 || vehicleYear > currentYear + 1) {
            showError('El año del vehículo no es válido');
            return false;
        }

        return true;
    };

    const handleSubmit = async (event) => {
        event.preventDefault();
        clearNotification();

        if (!validateForm()) {
            return;
        }

        try {
            let result;
            
            if (isEditMode) {
                // Actualizar vehículo existente
                result = await updateVehicle.execute(formData);
                showSuccess('Vehículo actualizado exitosamente');
            } else {
                // Crear nuevo vehículo
                result = await createVehicle.execute(formData);
                showSuccess('Vehículo creado exitosamente');
            }

            if (onSuccess) {
                onSuccess(result);
            }
        } catch (error) {
            showError(error.message || 'Error al procesar la solicitud');
        }
    };

    const isLoading = createVehicle.loading || updateVehicle.loading;

    const carTypes = [
        'SEDAN',
        'SUV',
        'CAMIONETA',
        'CAMIÓN',
        'MOTOCICLETA',
        'FURGONETA',
        'Auto Deportivo'
    ];

    return (
        <Card>
            <CardContent>
                <Typography variant="h5" component="h2" gutterBottom>
                    {isEditMode ? 'Editar' : 'Crear'} Vehículo
                </Typography>

                {notification && (
                    <Alert 
                        severity={notification.type} 
                        onClose={clearNotification}
                        sx={{ mb: 2 }}
                    >
                        {notification.message}
                    </Alert>
                )}

                <form onSubmit={handleSubmit}>
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="Número de Vehículo"
                                value={formData.vehicleNumber}
                                onChange={handleInputChange('vehicleNumber')}
                                required
                                disabled={isLoading}
                            />
                        </Grid>
                        
                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="Marca"
                                value={formData.vehicleMake}
                                onChange={handleInputChange('vehicleMake')}
                                required
                                disabled={isLoading}
                            />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="Modelo"
                                value={formData.vehicleModel}
                                onChange={handleInputChange('vehicleModel')}
                                required
                                disabled={isLoading}
                            />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <FormControl fullWidth required>
                                <InputLabel>Tipo de Vehículo</InputLabel>
                                <Select
                                    value={formData.carType}
                                    onChange={handleInputChange('carType')}
                                    disabled={isLoading}
                                >
                                    {carTypes.map((type) => (
                                        <MenuItem key={type} value={type}>
                                            {type}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="Color"
                                value={formData.vehicleColor}
                                onChange={handleInputChange('vehicleColor')}
                                required
                                disabled={isLoading}
                            />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="Año"
                                type="number"
                                value={formData.vehicleYear}
                                onChange={handleInputChange('vehicleYear')}
                                required
                                disabled={isLoading}
                            />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="Placa Estatal"
                                value={formData.stateLicensePlate}
                                onChange={handleInputChange('stateLicensePlate')}
                                required
                                disabled={isLoading}
                            />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="Número de Ejes"
                                type="number"
                                value={formData.axles}
                                onChange={handleInputChange('axles')}
                                disabled={isLoading}
                            />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="Capacidad (kg)"
                                type="number"
                                value={formData.capacity}
                                onChange={handleInputChange('capacity')}
                                disabled={isLoading}
                            />
                        </Grid>

                        {isEditMode && (
                            <>
                                <Grid item xs={12} sm={6}>
                                    <TextField
                                        fullWidth
                                        label="Número de Unidad"
                                        value={formData.unitNumber}
                                        onChange={handleInputChange('unitNumber')}
                                        disabled={isLoading}
                                    />
                                </Grid>

                                <Grid item xs={12} sm={6}>
                                    <TextField
                                        fullWidth
                                        label="Placa Federal"
                                        value={formData.federalLicensePlate}
                                        onChange={handleInputChange('federalLicensePlate')}
                                        disabled={isLoading}
                                    />
                                </Grid>

                                <Grid item xs={12}>
                                    <TextField
                                        fullWidth
                                        label="Notas del Vehículo"
                                        multiline
                                        rows={3}
                                        value={formData.vehicleNotes}
                                        onChange={handleInputChange('vehicleNotes')}
                                        disabled={isLoading}
                                    />
                                </Grid>

                                <Grid item xs={12} sm={6}>
                                    <FormControlLabel
                                        control={
                                            <Switch
                                                checked={formData.active}
                                                onChange={handleInputChange('active')}
                                                disabled={isLoading}
                                            />
                                        }
                                        label="Activo"
                                    />
                                </Grid>

                                <Grid item xs={12} sm={6}>
                                    <FormControlLabel
                                        control={
                                            <Switch
                                                checked={formData.approved}
                                                onChange={handleInputChange('approved')}
                                                disabled={isLoading}
                                            />
                                        }
                                        label="Aprobado"
                                    />
                                </Grid>
                            </>
                        )}

                        <Grid item xs={12}>
                            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                                {onCancel && (
                                    <Button
                                        variant="outlined"
                                        onClick={onCancel}
                                        disabled={isLoading}
                                    >
                                        Cancelar
                                    </Button>
                                )}
                                
                                <Button
                                    type="submit"
                                    variant="contained"
                                    disabled={isLoading}
                                    startIcon={isLoading && <CircularProgress size={20} />}
                                >
                                    {isLoading 
                                        ? 'Procesando...' 
                                        : isEditMode 
                                            ? 'Actualizar' 
                                            : 'Crear'
                                    }
                                </Button>
                            </Box>
                        </Grid>
                    </Grid>
                </form>
            </CardContent>
        </Card>
    );
};

export default VehicleFormWithApi;
