{"name": "web-app", "version": "1.0.0", "description": "Web Project", "private": true, "dependencies": {"@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mapbox/polyline": "1.1.1", "@material-ui/core": "^4.12.4", "@mui/icons-material": "^5.11.0", "@mui/lab": "^5.0.0-alpha.113", "@mui/material": "^5.11.1", "@mui/styles": "^5.11.1", "@mui/x-date-pickers": "^5.0.15", "@react-google-maps/api": "2.17.1", "@react-oauth/google": "^0.11.1", "apexcharts": "^3.36.3", "autosuggest-highlight": "3.2.0", "canvg": "^3.0.10", "classnames": "2.3.1", "common": "1.0.0", "dayjs": "^1.11.7", "i18next": "21.6.10", "material-table": "^2.0.3", "moment": "2.29.1", "prop-types": "15.8.1", "react": "18.2.0", "react-apexcharts": "^1.4.0", "react-dom": "18.2.0", "react-helmet-async": "^1.3.0", "react-i18next": "11.15.3", "react-places-autocomplete": "7.3.0", "react-redux": "7.2.6", "react-router-dom": "^6.6.1", "react-scripts": "4.0.3", "react-swipeable-views": "^0.14.0", "xlsx": "^0.18.5", "xlsx-saver": "^1.0.7"}, "scripts": {"start": "cross-env NODE_OPTIONS=--openssl-legacy-provider react-app-rewired start", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider react-app-rewired build", "eject": "react-app-rewired eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/preset-react": "^7.27.1", "cross-env": "^7.0.3", "customize-cra": "^1.0.0", "react-app-rewired": "^2.2.1"}}