{"build": {"development": {"channel": "development", "developmentClient": true, "distribution": "internal"}, "simulator": {"channel": "simulator", "developmentClient": true, "ios": {"simulator": true}, "distribution": "internal"}, "preview": {"channel": "preview", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease", "env": {"GRADLE_OPTS": "-Dorg.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8"}}, "ios": {"simulator": true}, "distribution": "internal", "env": {"EXPO_USE_DEV_SERVER": "1"}, "extends": "production"}, "minimal": {"channel": "preview", "android": {"buildType": "apk"}, "distribution": "internal"}, "production": {"channel": "production", "android": {"buildType": "app-bundle"}}}, "submit": {"production": {"android": {"serviceAccountKeyPath": "../serviceaccount.json", "track": "internal"}}}, "cli": {"version": ">= 0.50.0", "requireCommit": true}}