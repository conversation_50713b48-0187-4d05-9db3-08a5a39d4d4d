# Implementación de APIs de la Colección Postman

## Resumen de la Implementación

Se ha implementado exitosamente la integración de la colección de Postman "Apis-Art" en el proyecto completo. Esta implementación incluye:

### ✅ Endpoints Implementados en Firebase Functions

1. **createUser** - Crear clientes
2. **updateCustomer** - Actualizar clientes  
3. **createDriver** - Crear conductores
4. **updateDriver** - Actualizar conductores
5. **createVehicle** - Crear vehículos
6. **updateVehicle** - Actualizar vehículos
7. **receiveGroupedDelivery** - Crear entregas agrupadas *(NUEVO)*

### ✅ Servicios API para Aplicación Web

- **Archivo**: `web-app/src/services/apiService.js`
- **Hooks**: `web-app/src/hooks/useApiService.js`
- **Componentes**: 
  - `UserFormWithApi.js` - Formulario para clientes/conductores
  - `VehicleFormWithApi.js` - Formulario para vehículos
  - `GroupedDeliveryForm.js` - Formulario para entregas agrupadas

### ✅ Servicios API para Aplicación Móvil

- **Funciones**: Agregadas a `mobile-app/src/common/sharedFunctions.js`
- **Hooks**: `mobile-app/src/hooks/useApiService.js`

---

## Estructura de los Endpoints

### Base URL
```
https://us-central1-balle-813e3.cloudfunctions.net
```

### 1. Crear Cliente
**Endpoint**: `POST /createUser`

**Payload**:
```json
{
    "email": "<EMAIL>",
    "mobile": "+************",
    "password": "Seguridad2024",
    "firstName": "Nombre",
    "lastName": "Apellido",
    "nombreComercial": "Mi Empresa S.A. de C.V.",
    "rfc": "XAXX010101000",
    "city": "Guadalajara"
}
```

**Respuesta**:
```json
{
    "success": true,
    "message": "Usuario creado exitosamente",
    "userId": "uid_generado",
    "data": { /* datos del usuario */ }
}
```

### 2. Actualizar Cliente
**Endpoint**: `POST /updateCustomer`

**Payload**:
```json
{
    "uid": "uid_del_usuario",
    "firstName": "Nuevo Nombre",
    "lastName": "Nuevo Apellido",
    "email": "<EMAIL>",
    "mobile": "+************",
    "nombreComercial": "Nueva Empresa S.A. de C.V.",
    "rfc": "XAXX010101000",
    "city": "Guadalajara"
}
```

### 3. Crear Conductor
**Endpoint**: `POST /createDriver`

**Payload**:
```json
{
    "firstName": "Conductor",
    "lastName": "Apellido",
    "email": "<EMAIL>",
    "mobile": "+************",
    "password": "Seguridad2024",
    "hireDate": 1712000000000
}
```

### 4. Actualizar Conductor
**Endpoint**: `PUT /updateDriver`

**Payload**:
```json
{
    "uid": "uid_del_conductor",
    "firstName": "Conductor Actualizado",
    "lastName": "Apellido Actualizado",
    "email": "<EMAIL>",
    "mobile": "+************"
}
```

### 5. Crear Vehículo
**Endpoint**: `POST /createVehicle`

**Payload**:
```json
{
    "vehicleNumber": "ABC123456",
    "vehicleMake": "Ford",
    "vehicleModel": "F-150",
    "axles": 2,
    "capacity": 40000,
    "carType": "CAMIONETA",
    "vehicleColor": "Blanco",
    "vehicleYear": "2024",
    "stateLicensePlate": "XYZ2025"
}
```

### 6. Actualizar Vehículo
**Endpoint**: `PUT /updateVehicle`

**Payload**:
```json
{
    "vehicleId": "veh_112233",
    "vehicleNumber": "TRK-0456",
    "vehicleMake": "Ford",
    "vehicleModel": "F-150",
    "carType": "CAMIONETA",
    "active": true,
    "approved": true,
    "driver": "uid_del_conductor",
    "unitNumber": "UN1234",
    "stateLicensePlate": "ABC1234",
    "vehicleColor": "Blanco",
    "vehicleYear": "2022"
}
```

### 7. Crear Entrega Agrupada *(NUEVO)*
**Endpoint**: `POST /receiveGroupedDelivery`

**Payload**:
```json
{
    "folio": "ENT-2024-001",
    "driver": "uid_del_conductor",
    "vehicle": {
        "type": "CAMIONETA",
        "image": "https://example.com/truck.png",
        "plate": "ABC-123"
    },
    "orders": [
        {
            "customerId": "uid_del_cliente",
            "customerName": "Nombre Cliente",
            "customerEmail": "<EMAIL>",
            "customerPhone": "+************",
            "pickupAddress": {
                "lat": 19.4326,
                "lng": -99.1332,
                "address": "Almacén Central, CDMX"
            },
            "deliveryAddress": {
                "lat": 19.4327,
                "lng": -99.1333,
                "address": "Av. Reforma 123, CDMX"
            },
            "estimatedFare": 150.00,
            "estimatedDistance": 5.2,
            "estimatedTime": 15,
            "notes": "Entregar antes de las 2 PM",
            "products": [
                {
                    "id": "PROD-001",
                    "name": "Laptop Dell XPS 13",
                    "quantity": 1,
                    "sku": "DELL-XPS-13-001"
                }
            ]
        }
    ],
    "estimatedRoute": [
        {"lat": 19.4326, "lng": -99.1332},
        {"lat": 19.4327, "lng": -99.1333}
    ],
    "totalDistance": 13.3,
    "totalEstimatedTime": 35
}
```

---

## Ejemplos de Uso

### Aplicación Web (React)

#### Ejemplo 1: Crear Cliente
```javascript
import { useCustomerService } from '../hooks/useApiService';

const CreateCustomerComponent = () => {
    const { createCustomer } = useCustomerService();

    const handleCreateCustomer = async () => {
        try {
            const customerData = {
                firstName: 'Juan',
                lastName: 'Pérez',
                email: '<EMAIL>',
                mobile: '+************',
                password: 'MiPassword123',
                nombreComercial: 'Empresa Juan S.A.',
                rfc: 'XAXX010101000',
                city: 'Guadalajara'
            };

            const result = await createCustomer.execute(customerData);
            console.log('Cliente creado:', result);
        } catch (error) {
            console.error('Error:', error.message);
        }
    };

    return (
        <button 
            onClick={handleCreateCustomer}
            disabled={createCustomer.loading}
        >
            {createCustomer.loading ? 'Creando...' : 'Crear Cliente'}
        </button>
    );
};
```

#### Ejemplo 2: Crear Entrega Agrupada
```javascript
import { useDeliveryService } from '../hooks/useApiService';

const CreateDeliveryComponent = () => {
    const { createGroupedDelivery } = useDeliveryService();

    const handleCreateDelivery = async () => {
        try {
            const deliveryData = {
                folio: 'ENT-2024-001',
                driver: 'driver_uid_123',
                vehicle: {
                    type: 'CAMIONETA',
                    plate: 'ABC-123'
                },
                orders: [
                    {
                        customerId: 'customer_uid_456',
                        customerName: 'María García',
                        customerEmail: '<EMAIL>',
                        customerPhone: '+************',
                        pickupAddress: {
                            lat: 19.4326,
                            lng: -99.1332,
                            address: 'Almacén Central'
                        },
                        deliveryAddress: {
                            lat: 19.4327,
                            lng: -99.1333,
                            address: 'Destino Final'
                        },
                        products: [
                            {
                                id: 'PROD-001',
                                name: 'Producto de Prueba',
                                quantity: 2,
                                sku: 'SKU-001'
                            }
                        ]
                    }
                ]
            };

            const result = await createGroupedDelivery.execute(deliveryData);
            console.log('Entrega creada:', result);
        } catch (error) {
            console.error('Error:', error.message);
        }
    };

    return (
        <button 
            onClick={handleCreateDelivery}
            disabled={createGroupedDelivery.loading}
        >
            {createGroupedDelivery.loading ? 'Creando...' : 'Crear Entrega'}
        </button>
    );
};
```

### Aplicación Móvil (React Native)

#### Ejemplo 1: Crear Conductor
```javascript
import { useDriverService } from '../hooks/useApiService';

const CreateDriverScreen = () => {
    const { createDriver } = useDriverService();

    const handleCreateDriver = async () => {
        try {
            const driverData = {
                firstName: 'Carlos',
                lastName: 'Rodríguez',
                email: '<EMAIL>',
                mobile: '+************',
                password: 'DriverPass123'
            };

            const result = await createDriver.execute(driverData);
            Alert.alert('Éxito', 'Conductor creado exitosamente');
        } catch (error) {
            Alert.alert('Error', error.message);
        }
    };

    return (
        <TouchableOpacity 
            onPress={handleCreateDriver}
            disabled={createDriver.loading}
        >
            <Text>
                {createDriver.loading ? 'Creando...' : 'Crear Conductor'}
            </Text>
        </TouchableOpacity>
    );
};
```

#### Ejemplo 2: Actualizar Vehículo
```javascript
import { useVehicleService } from '../hooks/useApiService';

const UpdateVehicleScreen = ({ vehicleId }) => {
    const { updateVehicle } = useVehicleService();

    const handleUpdateVehicle = async () => {
        try {
            const vehicleData = {
                vehicleId: vehicleId,
                vehicleNumber: 'TRK-0456',
                vehicleMake: 'Ford',
                vehicleModel: 'F-150',
                carType: 'CAMIONETA',
                active: true,
                approved: true
            };

            const result = await updateVehicle.execute(vehicleData);
            Alert.alert('Éxito', 'Vehículo actualizado exitosamente');
        } catch (error) {
            Alert.alert('Error', error.message);
        }
    };

    return (
        <TouchableOpacity 
            onPress={handleUpdateVehicle}
            disabled={updateVehicle.loading}
        >
            <Text>
                {updateVehicle.loading ? 'Actualizando...' : 'Actualizar Vehículo'}
            </Text>
        </TouchableOpacity>
    );
};
```

---

## Validaciones Implementadas

### Validaciones de Cliente/Conductor
- ✅ Email válido
- ✅ Teléfono con código de país (+52)
- ✅ Campos requeridos
- ✅ Contraseña mínima (6 caracteres)

### Validaciones de Vehículo
- ✅ Campos requeridos
- ✅ Año válido (1900 - año actual + 1)
- ✅ Formato de datos

### Validaciones de Entrega Agrupada
- ✅ Al menos una orden
- ✅ Productos en cada orden
- ✅ Direcciones válidas
- ✅ Datos del conductor y vehículo

---

## Manejo de Errores

Todos los servicios incluyen manejo robusto de errores:

```javascript
try {
    const result = await apiService.execute(data);
    // Manejar éxito
} catch (error) {
    // error.message contiene el mensaje de error
    console.error('Error:', error.message);
}
```

---

## Próximos Pasos

1. **Testing**: Probar todos los endpoints con datos reales
2. **Integración**: Conectar con componentes existentes del proyecto
3. **Optimización**: Agregar caché y optimizaciones de rendimiento
4. **Documentación**: Expandir documentación según necesidades específicas

---

## Archivos Creados/Modificados

### Backend (Firebase Functions)
- ✅ `functions/index.js` - Agregado endpoint `receiveGroupedDelivery`

### Frontend Web
- ✅ `web-app/src/services/apiService.js` - Servicios API
- ✅ `web-app/src/hooks/useApiService.js` - Hooks React
- ✅ `web-app/src/components/UserFormWithApi.js` - Formulario usuarios
- ✅ `web-app/src/components/VehicleFormWithApi.js` - Formulario vehículos
- ✅ `web-app/src/components/GroupedDeliveryForm.js` - Formulario entregas

### Frontend Móvil
- ✅ `mobile-app/src/common/sharedFunctions.js` - Funciones API
- ✅ `mobile-app/src/hooks/useApiService.js` - Hooks React Native

### Documentación
- ✅ `IMPLEMENTACION_APIS_POSTMAN.md` - Este archivo
- ✅ `EJEMPLOS_INTEGRACION.md` - Ejemplos prácticos de integración
