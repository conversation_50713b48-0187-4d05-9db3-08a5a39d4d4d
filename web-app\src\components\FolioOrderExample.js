import React, { useState } from 'react';
import MaterialTable from 'material-table';
import { ThemeProvider } from '@mui/material/styles';
import theme from '../styles/tableStyle';
import { useTranslation } from 'react-i18next';
import { colors } from './Theme/WebTheme';

// Componente de ejemplo para mostrar la funcionalidad de folio/orden colapsable
const FolioOrderExample = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir();

  // Datos de ejemplo que simulan entregas con folio y orden
  const sampleData = [
    {
      id: 1,
      reference: 'GKBPPS',
      deliveryDocument: '',
      folio: 'ENT-2024-001',
      orderIndex: 1,
      orderDetails: 'Productos: 3x Laptop, 2x Mouse',
      customer_name: '<PERSON>',
      bookingDate: new Date('2025-07-20T09:45:00'),
      carType: 'CAMIONETA',
      driver_name: '<PERSON>',
      status: 'COMPLETE',
      otp: 'false'
    },
    {
      id: 2,
      reference: 'WADKNU',
      deliveryDocument: 'OCG-968598',
      folio: 'ENT-2024-002',
      orderIndex: 2,
      orderDetails: 'Productos: 1x Impresora, 5x Cartuchos',
      customer_name: 'Jonathan Ballesteros',
      bookingDate: new Date('2025-04-04T13:24:00'),
      carType: 'MOTOCICLETA',
      driver_name: 'Diego Ballesteros',
      status: 'COMPLETE',
      otp: '16951'
    },
    {
      id: 3,
      reference: 'GDY496Y3',
      deliveryDocument: '',
      folio: 'ENT-2024-001',
      orderIndex: 3,
      orderDetails: 'Productos: 2x Monitor, 1x Teclado',
      customer_name: 'Juan Pérez Test',
      bookingDate: new Date('2025-07-23T10:47:00'),
      carType: 'CAMIONETA',
      driver_name: 'Diego Ballesteros',
      status: 'COMPLETE',
      otp: 'false'
    },
    {
      id: 4,
      reference: 'GDNRBL1N',
      deliveryDocument: '',
      folio: 'ENT-2024-003',
      orderIndex: 1,
      orderDetails: 'Productos: 1x Tablet, 1x Funda',
      customer_name: 'María García Test',
      bookingDate: new Date('2025-07-23T10:47:00'),
      carType: 'CAMIONETA',
      driver_name: 'Diego Ballesteros',
      status: 'COMPLETE',
      otp: 'false'
    }
  ];

  // Componente para la columna de folio/orden colapsable
  const FolioOrderCell = ({ rowData }) => {
    const [expanded, setExpanded] = useState(false);
    
    return (
      <div style={{ cursor: 'pointer', minWidth: '120px' }}>
        <div 
          onClick={() => setExpanded(!expanded)}
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '6px 10px',
            backgroundColor: expanded ? '#e3f2fd' : '#f5f5f5',
            borderRadius: '6px',
            border: '1px solid #ddd',
            transition: 'all 0.3s ease',
            boxShadow: expanded ? '0 2px 8px rgba(0,0,0,0.15)' : '0 1px 3px rgba(0,0,0,0.1)'
          }}
        >
          <span style={{ 
            fontWeight: 'bold', 
            fontSize: '13px',
            color: '#333'
          }}>
            {rowData.folio || 'N/A'}
          </span>
          <span style={{ 
            fontSize: '12px', 
            color: '#666',
            transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
            transition: 'transform 0.3s ease',
            marginLeft: '8px'
          }}>
            ▼
          </span>
        </div>
        {expanded && (
          <div style={{
            marginTop: '6px',
            padding: '8px 10px',
            backgroundColor: '#fff',
            border: '1px solid #ddd',
            borderRadius: '6px',
            fontSize: '12px',
            color: '#555',
            boxShadow: '0 3px 10px rgba(0,0,0,0.15)',
            animation: 'slideDown 0.3s ease'
          }}>
            <div style={{ 
              marginBottom: '4px',
              display: 'flex',
              alignItems: 'center'
            }}>
              <strong style={{ color: '#1976d2' }}>{t('orden')}:</strong> 
              <span style={{ 
                marginLeft: '6px',
                backgroundColor: '#1976d2',
                color: 'white',
                padding: '2px 6px',
                borderRadius: '10px',
                fontSize: '10px',
                fontWeight: 'bold'
              }}>
                #{rowData.orderIndex || 'N/A'}
              </span>
            </div>
            {rowData.orderDetails && (
              <div style={{ 
                fontSize: '11px', 
                color: '#777',
                marginTop: '4px',
                padding: '4px',
                backgroundColor: '#f8f9fa',
                borderRadius: '4px',
                borderLeft: '3px solid #1976d2'
              }}>
                {rowData.orderDetails}
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  // Definición de columnas
  const columns = [
    { 
      title: t('booking_ref'), 
      field: 'reference',
      width: '10%'
    },
    { 
      title: t('DOCUMENTO DE ENTREGA'), 
      field: 'deliveryDocument',
      width: '12%',
      render: rowData => rowData.deliveryDocument || '-'
    },
    { 
      title: t('folio_orden'), 
      field: 'folio',
      width: '15%',
      render: (rowData) => <FolioOrderCell rowData={rowData} />
    },
    { 
      title: t('customer_name'), 
      field: 'customer_name',
      width: '15%'
    },
    { 
      title: t('booking_date'), 
      field: 'bookingDate',
      width: '15%',
      render: rowData => rowData.bookingDate ? 
        rowData.bookingDate.toLocaleDateString() + ' ' + 
        rowData.bookingDate.toLocaleTimeString() : null
    },
    { 
      title: t('car_type'), 
      field: 'carType',
      width: '10%'
    },
    { 
      title: t('assign_driver'), 
      field: 'driver_name',
      width: '15%'
    },
    { 
      title: t('booking_status_web'), 
      field: 'status',
      width: '8%',
      render: rowData => (
        <div style={{
          backgroundColor: rowData.status === "CANCELLED" ? colors.RED : 
                          rowData.status === "COMPLETE" ? colors.GREEN : colors.YELLOW,
          color: "white",
          padding: 7,
          borderRadius: "15px",
          fontWeight: "bold",
          width: "100px",
          margin: 'auto',
          textAlign: 'center',
          fontSize: '11px'
        }}>
          {t(rowData.status)}
        </div>
      )
    }
  ];

  return (
    <div style={{ padding: '20px' }}>
      <style>
        {`
          @keyframes slideDown {
            from {
              opacity: 0;
              transform: translateY(-10px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
        `}
      </style>
      
      <h2 style={{ 
        marginBottom: '20px', 
        color: '#333',
        fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif'
      }}>
        Ejemplo: Tabla con Folio/Orden Colapsable
      </h2>
      
      <div style={{
        backgroundColor: '#f8f9fa',
        padding: '15px',
        borderRadius: '8px',
        marginBottom: '20px',
        border: '1px solid #dee2e6'
      }}>
        <h4 style={{ margin: '0 0 10px 0', color: '#495057' }}>Instrucciones:</h4>
        <ul style={{ margin: 0, paddingLeft: '20px', color: '#6c757d' }}>
          <li>Haz clic en cualquier celda de la columna "Folio" para expandir/colapsar</li>
          <li>Al expandir verás el número de orden y detalles adicionales</li>
          <li>La animación suave mejora la experiencia del usuario</li>
          <li>El diseño es responsive y se adapta al contenido</li>
        </ul>
      </div>

      <ThemeProvider theme={theme}>
        <MaterialTable
          title="Mis entregas - Ejemplo con Folio/Orden"
          style={{
            direction: isRTL === "rtl" ? "rtl" : "ltr",
            borderRadius: "8px",
            boxShadow: "0px 2px 5px rgba(0,0,0,0.1)",
          }}
          columns={columns}
          data={sampleData}
          options={{
            pageSize: 10,
            pageSizeOptions: [5, 10, 15],
            search: true,
            sorting: true,
            headerStyle: {
              backgroundColor: '#f5f5f5',
              color: '#333',
              fontWeight: 'bold',
              textAlign: 'center',
              fontSize: '13px'
            },
            cellStyle: {
              textAlign: 'center',
              fontSize: '12px'
            },
            rowStyle: {
              '&:hover': {
                backgroundColor: '#f8f9fa'
              }
            }
          }}
          localization={{
            toolbar: {
              searchPlaceholder: t("search") || "Buscar",
            },
            pagination: {
              labelDisplayedRows: "{from}-{to} de {count}",
              firstTooltip: "Primera página",
              previousTooltip: "Página anterior",
              nextTooltip: "Página siguiente",
              lastTooltip: "Última página",
            },
          }}
        />
      </ThemeProvider>
    </div>
  );
};

export default FolioOrderExample;
