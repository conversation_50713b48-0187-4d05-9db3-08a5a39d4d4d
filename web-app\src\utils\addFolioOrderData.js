/**
 * Utilidad para agregar datos de folio y orden a los bookings existentes
 * Esto es para demostrar la funcionalidad de folio/orden colapsable
 */

// Función para generar folios únicos
const generateFolio = (index) => {
  const year = new Date().getFullYear();
  const folioNumber = String(index + 1).padStart(3, '0');
  return `ENT-${year}-${folioNumber}`;
};

// Función para generar detalles de productos aleatorios
const generateProductDetails = () => {
  const products = [
    'Laptop', 'Mouse', 'Teclado', 'Monitor', 'Impresora', 
    'Cartuchos', 'Tablet', 'Funda', 'Cargador', 'Auriculares',
    'Webcam', 'Micrófono', 'Disco Duro', 'Memoria USB', 'Router'
  ];
  
  const numProducts = Math.floor(Math.random() * 3) + 1; // 1-3 productos
  const selectedProducts = [];
  
  for (let i = 0; i < numProducts; i++) {
    const product = products[Math.floor(Math.random() * products.length)];
    const quantity = Math.floor(Math.random() * 5) + 1; // 1-5 cantidad
    selectedProducts.push(`${quantity}x ${product}`);
  }
  
  return `Productos: ${selectedProducts.join(', ')}`;
};

// Función para agregar datos de folio y orden a un array de bookings
export const addFolioOrderData = (bookings) => {
  if (!bookings || !Array.isArray(bookings)) {
    return bookings;
  }

  // Agrupar bookings por cliente para simular entregas agrupadas
  const groupedByCustomer = {};
  
  bookings.forEach((booking, index) => {
    const customerKey = booking.customer_name || booking.customer || 'unknown';
    
    if (!groupedByCustomer[customerKey]) {
      groupedByCustomer[customerKey] = [];
    }
    
    groupedByCustomer[customerKey].push({ ...booking, originalIndex: index });
  });

  // Asignar folios y órdenes
  let folioCounter = 0;
  const updatedBookings = [...bookings];

  Object.keys(groupedByCustomer).forEach(customerKey => {
    const customerBookings = groupedByCustomer[customerKey];
    
    // Decidir si este cliente tiene entregas agrupadas (50% de probabilidad)
    const hasGroupedDelivery = Math.random() > 0.5 && customerBookings.length > 1;
    
    if (hasGroupedDelivery) {
      // Usar el mismo folio para todas las entregas del cliente
      const sharedFolio = generateFolio(folioCounter++);
      
      customerBookings.forEach((booking, orderIndex) => {
        const updatedBooking = {
          ...booking,
          folio: sharedFolio,
          orderIndex: orderIndex + 1,
          orderDetails: generateProductDetails(),
          deliveryType: 'grouped_delivery'
        };
        
        updatedBookings[booking.originalIndex] = updatedBooking;
      });
    } else {
      // Cada entrega tiene su propio folio
      customerBookings.forEach((booking) => {
        const updatedBooking = {
          ...booking,
          folio: generateFolio(folioCounter++),
          orderIndex: 1,
          orderDetails: generateProductDetails(),
          deliveryType: 'individual_delivery'
        };
        
        updatedBookings[booking.originalIndex] = updatedBooking;
      });
    }
  });

  return updatedBookings;
};

// Función para agregar datos de ejemplo a un booking individual
export const addSampleFolioData = (booking, folioIndex = 1, orderIndex = 1) => {
  return {
    ...booking,
    folio: generateFolio(folioIndex),
    orderIndex: orderIndex,
    orderDetails: generateProductDetails(),
    deliveryType: 'sample_delivery'
  };
};

// Datos de ejemplo para testing
export const sampleBookingsWithFolio = [
  {
    id: 'sample1',
    reference: 'GKBPPS',
    deliveryDocument: '',
    folio: 'ENT-2024-001',
    orderIndex: 1,
    orderDetails: 'Productos: 3x Laptop, 2x Mouse',
    customer_name: 'Jonathan Ballesteros',
    bookingDate: new Date('2025-07-20T09:45:00'),
    carType: 'CAMIONETA',
    driver_name: 'Diego Ballesteros',
    status: 'COMPLETE',
    otp: 'false'
  },
  {
    id: 'sample2',
    reference: 'WADKNU',
    deliveryDocument: 'OCG-968598',
    folio: 'ENT-2024-002',
    orderIndex: 2,
    orderDetails: 'Productos: 1x Impresora, 5x Cartuchos',
    customer_name: 'Jonathan Ballesteros',
    bookingDate: new Date('2025-04-04T13:24:00'),
    carType: 'MOTOCICLETA',
    driver_name: 'Diego Ballesteros',
    status: 'COMPLETE',
    otp: '16951'
  },
  {
    id: 'sample3',
    reference: 'GDY496Y3',
    deliveryDocument: '',
    folio: 'ENT-2024-001',
    orderIndex: 3,
    orderDetails: 'Productos: 2x Monitor, 1x Teclado',
    customer_name: 'Juan Pérez Test',
    bookingDate: new Date('2025-07-23T10:47:00'),
    carType: 'CAMIONETA',
    driver_name: 'Diego Ballesteros',
    status: 'COMPLETE',
    otp: 'false'
  },
  {
    id: 'sample4',
    reference: 'GDNRBL1N',
    deliveryDocument: '',
    folio: 'ENT-2024-003',
    orderIndex: 1,
    orderDetails: 'Productos: 1x Tablet, 1x Funda',
    customer_name: 'María García Test',
    bookingDate: new Date('2025-07-23T10:47:00'),
    carType: 'CAMIONETA',
    driver_name: 'Diego Ballesteros',
    status: 'COMPLETE',
    otp: 'false'
  }
];

export default {
  addFolioOrderData,
  addSampleFolioData,
  sampleBookingsWithFolio
};
