# CONFIGURACIÓN DEL ENDPOINT receiveGroupedDelivery

## INFORMACIÓN GENERAL

**URL del Endpoint:** `https://us-central1-balle-813e3.cloudfunctions.net/receiveGroupedDelivery`

**Método HTTP:** `POST`

**Descripción:** Endpoint para crear entregas agrupadas que incluye múltiples órdenes de entrega en una sola solicitud. Cada orden se convierte automáticamente en un booking individual en el sistema.

**Versión de la App iOS:** 1.0.2

---

## HEADERS REQUERIDOS

```
Content-Type: application/json
Access-Control-Allow-Origin: *
Access-Control-Allow-Headers: Content-Type
```

---

## ESTRUCTURA DEL REQUEST BODY

### Campos Principales Requeridos

| Campo | Tipo | Requerido | Descripción |
|-------|------|-----------|-------------|
| `folio` | String | ✅ | Identificador único de la entrega agrupada |
| `driver` | String | ✅ | UID del conductor asignado |
| `vehicle` | Object | ✅ | Información del vehículo |
| `orders` | Array | ✅ | Array de órdenes de entrega (mínimo 1) |

### Campos Opcionales

| Campo | Tipo | Requerido | Descripción |
|-------|------|-----------|-------------|
| `estimatedRoute` | Array | ❌ | Ruta estimada con coordenadas |
| `totalDistance` | Number | ❌ | Distancia total en kilómetros |
| `totalEstimatedTime` | Number | ❌ | Tiempo estimado total en minutos |

---

## ESTRUCTURA DEL OBJETO VEHICLE

```json
{
  "type": "CAMIONETA",
  "plate": "ABC-123",
  "image": "https://example.com/vehicle.jpg"
}
```

| Campo | Tipo | Requerido | Descripción |
|-------|------|-----------|-------------|
| `type` | String | ✅ | Tipo de vehículo |
| `plate` | String | ✅ | Placa del vehículo |
| `image` | String | ❌ | URL de la imagen del vehículo |

---

## ESTRUCTURA DEL OBJETO ORDER

### Campos Requeridos por Orden

| Campo | Tipo | Requerido | Descripción |
|-------|------|-----------|-------------|
| `customerId` | String | ✅ | UID del cliente |
| `customerName` | String | ✅ | Nombre completo del cliente |
| `customerEmail` | String | ✅ | Email del cliente |
| `pickupAddress` | Object | ✅ | Dirección de recogida |
| `deliveryAddress` | Object | ✅ | Dirección de entrega |
| `products` | Array | ✅ | Lista de productos (mínimo 1) |

### Campos Opcionales por Orden

| Campo | Tipo | Requerido | Descripción |
|-------|------|-----------|-------------|
| `customerPhone` | String | ❌ | Teléfono del cliente |
| `customerToken` | String | ❌ | Token push del cliente |
| `estimatedFare` | Number | ❌ | Tarifa estimada |
| `estimatedDistance` | Number | ❌ | Distancia estimada en km |
| `estimatedTime` | Number | ❌ | Tiempo estimado en minutos |
| `notes` | String | ❌ | Notas adicionales |

---

## ESTRUCTURA DE DIRECCIONES (pickupAddress/deliveryAddress)

```json
{
  "lat": 19.4326,
  "lng": -99.1332,
  "address": "Dirección completa"
}
```

| Campo | Tipo | Requerido | Descripción |
|-------|------|-----------|-------------|
| `lat` | Number | ✅ | Latitud |
| `lng` | Number | ✅ | Longitud |
| `address` | String | ✅ | Dirección completa en texto |

---

## ESTRUCTURA DE PRODUCTOS

```json
{
  "id": "PROD-001",
  "name": "Nombre del Producto",
  "quantity": 2,
  "sku": "SKU-001",
  "description": "Descripción del producto"
}
```

| Campo | Tipo | Requerido | Descripción |
|-------|------|-----------|-------------|
| `id` | String | ✅ | ID único del producto |
| `name` | String | ✅ | Nombre del producto |
| `quantity` | Number | ✅ | Cantidad |
| `sku` | String | ❌ | Código SKU |
| `description` | String | ❌ | Descripción del producto |

---

## EJEMPLO COMPLETO DE REQUEST

```json
{
  "folio": "ENT-2024-001",
  "driver": "driver_uid_123",
  "vehicle": {
    "type": "CAMIONETA",
    "plate": "ABC-123",
    "image": "https://example.com/vehicle.jpg"
  },
  "orders": [
    {
      "customerId": "customer_uid_456",
      "customerName": "María García",
      "customerEmail": "<EMAIL>",
      "customerPhone": "+************",
      "customerToken": "push_token_123",
      "pickupAddress": {
        "lat": 19.4326,
        "lng": -99.1332,
        "address": "Almacén Central, Calle Principal 123"
      },
      "deliveryAddress": {
        "lat": 19.4327,
        "lng": -99.1333,
        "address": "Destino Final, Avenida Secundaria 456"
      },
      "estimatedFare": 150.00,
      "estimatedDistance": 5.2,
      "estimatedTime": 25,
      "notes": "Entrega urgente - manejar con cuidado",
      "products": [
        {
          "id": "PROD-001",
          "name": "Producto Ejemplo 1",
          "quantity": 2,
          "sku": "SKU-001",
          "description": "Descripción del producto"
        }
      ]
    }
  ],
  "estimatedRoute": [
    { "lat": 19.4326, "lng": -99.1332 },
    { "lat": 19.4327, "lng": -99.1333 }
  ],
  "totalDistance": 5.2,
  "totalEstimatedTime": 25
}
```

---

## RESPUESTAS DEL ENDPOINT

### Respuesta Exitosa (200)

```json
{
  "success": true,
  "message": "Entrega agrupada creada exitosamente con bookings individuales",
  "deliveryId": "generated_delivery_id",
  "folio": "ENT-2024-001",
  "ordersCount": 1,
  "bookingsCreated": ["booking_id_1", "booking_id_2"]
}
```

### Respuesta de Error (400)

```json
{
  "success": false,
  "error": "Descripción del error específico"
}
```

---

## VALIDACIONES IMPLEMENTADAS

### Validaciones de Campos Requeridos
- ✅ Presencia de `folio`, `driver`, `vehicle`, `orders`
- ✅ `orders` debe ser un array con al menos 1 elemento
- ✅ Cada orden debe tener todos los campos requeridos
- ✅ Cada orden debe tener al menos 1 producto

### Validaciones de Estructura
- ✅ Formato correcto de direcciones (lat, lng, address)
- ✅ Estructura válida del objeto vehicle
- ✅ Estructura válida de productos

---

## FUNCIONALIDADES ADICIONALES

### Creación Automática de Bookings
- Cada orden se convierte automáticamente en un booking individual
- Los bookings siguen el flujo normal del sistema de entregas
- Se mantiene la vinculación entre la entrega agrupada y los bookings

### Notificaciones Push
- Se envía notificación automática al conductor asignado
- La notificación incluye el número total de órdenes
- Incluye datos específicos de la entrega agrupada

### Almacenamiento en Base de Datos
- **Tabla `groupedDeliveries`**: Información principal de la entrega agrupada
- **Tabla `orders`**: Órdenes individuales vinculadas a la entrega
- **Tabla `bookings`**: Bookings individuales para seguimiento de entregas

---

## CÓDIGOS DE ERROR COMUNES

| Código | Descripción | Solución |
|--------|-------------|----------|
| 400 | Faltan campos requeridos | Verificar que todos los campos obligatorios estén presentes |
| 400 | Orders debe ser un array | Asegurar que `orders` sea un array con al menos 1 elemento |
| 400 | Orden X tiene campos faltantes | Verificar estructura completa de cada orden |
| 500 | Error interno del servidor | Contactar soporte técnico |

---

## NOTAS TÉCNICAS

- **Timeout**: 60 segundos máximo por request
- **Tamaño máximo**: 10MB por request
- **Límite de órdenes**: Recomendado máximo 50 órdenes por entrega agrupada
- **Formato de fechas**: Timestamps en milisegundos (Unix timestamp)
- **Codificación**: UTF-8

---

## TESTING

Para probar el endpoint, se puede usar el archivo `test_grouped_delivery.js` incluido en el proyecto, que contiene un ejemplo completo de uso.

**Comando de prueba:**
```bash
node test_grouped_delivery.js
```
