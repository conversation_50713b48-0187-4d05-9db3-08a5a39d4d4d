# Ejemplos Prácticos de Integración

## Cómo Integrar los Nuevos Servicios API en Componentes Existentes

### 1. Integración en EditUser.js (Aplicación Web)

#### Antes (usando Redux):
```javascript
// En EditUser.js línea 404
dispatch(editUser(data.id, { ...data }));
```

#### Después (usando nuevos servicios API):
```javascript
import { useCustomerService } from '../hooks/useApiService';

function EditUser() {
    const { updateCustomer } = useCustomerService();
    
    // ... resto del código ...
    
    const handleSave = async () => {
        try {
            setLoading(true);
            
            if (data.usertype === 'customer') {
                await updateCustomer.execute(data);
            } else if (data.usertype === 'driver') {
                // Usar updateDriver si es conductor
                await updateDriver.execute(data);
            }
            
            setCommonAlert({ 
                open: true, 
                msg: "Usuario actualizado exitosamente" 
            });
            navigateToInfo();
            
        } catch (error) {
            setCommonAlert({ 
                open: true, 
                msg: error.message 
            });
        } finally {
            setLoading(false);
        }
    };
}
```

### 2. Integración en CarsList.js (Gestión de Vehículos)

```javascript
import { useVehicleService } from '../hooks/useApiService';

function CarsList() {
    const { createVehicle, updateVehicle } = useVehicleService();
    
    const handleAddVehicle = async (vehicleData) => {
        try {
            const result = await createVehicle.execute(vehicleData);
            // Actualizar lista de vehículos
            fetchVehicles();
            showSuccessMessage('Vehículo creado exitosamente');
        } catch (error) {
            showErrorMessage(error.message);
        }
    };
    
    const handleUpdateVehicle = async (vehicleId, vehicleData) => {
        try {
            const dataWithId = { ...vehicleData, vehicleId };
            await updateVehicle.execute(dataWithId);
            fetchVehicles();
            showSuccessMessage('Vehículo actualizado exitosamente');
        } catch (error) {
            showErrorMessage(error.message);
        }
    };
}
```

### 3. Integración en AddBookings.js (Crear Entregas)

```javascript
import { useDeliveryService } from '../hooks/useApiService';

function AddBookings() {
    const { createGroupedDelivery } = useDeliveryService();
    
    const handleCreateGroupedDelivery = async (bookingData) => {
        try {
            // Transformar datos del booking a formato de entrega agrupada
            const deliveryData = {
                folio: generateFolio(),
                driver: bookingData.driverId,
                vehicle: {
                    type: bookingData.carType,
                    plate: bookingData.vehiclePlate,
                    image: bookingData.vehicleImage
                },
                orders: [{
                    customerId: bookingData.customerId,
                    customerName: bookingData.customerName,
                    customerEmail: bookingData.customerEmail,
                    customerPhone: bookingData.customerPhone,
                    pickupAddress: {
                        lat: bookingData.pickup.lat,
                        lng: bookingData.pickup.lng,
                        address: bookingData.pickup.add
                    },
                    deliveryAddress: {
                        lat: bookingData.drop.lat,
                        lng: bookingData.drop.lng,
                        address: bookingData.drop.add
                    },
                    estimatedFare: bookingData.estimate,
                    products: bookingData.products || []
                }]
            };
            
            const result = await createGroupedDelivery.execute(deliveryData);
            showSuccessMessage('Entrega agrupada creada exitosamente');
            
        } catch (error) {
            showErrorMessage(error.message);
        }
    };
}
```

### 4. Integración en Registration.js (Aplicación Móvil)

```javascript
import { useCustomerService, useDriverService } from '../hooks/useApiService';

const Registration = ({ userType }) => {
    const { createCustomer } = useCustomerService();
    const { createDriver } = useDriverService();
    
    const handleRegistration = async (formData) => {
        try {
            let result;
            
            if (userType === 'customer') {
                result = await createCustomer.execute(formData);
            } else if (userType === 'driver') {
                result = await createDriver.execute(formData);
            }
            
            Alert.alert(
                'Éxito',
                'Usuario registrado exitosamente',
                [{ text: 'OK', onPress: () => navigation.navigate('Login') }]
            );
            
        } catch (error) {
            Alert.alert('Error', error.message);
        }
    };
    
    return (
        <View>
            {/* Formulario de registro */}
            <TouchableOpacity 
                onPress={() => handleRegistration(formData)}
                disabled={createCustomer.loading || createDriver.loading}
            >
                <Text>
                    {(createCustomer.loading || createDriver.loading) 
                        ? 'Registrando...' 
                        : 'Registrar'
                    }
                </Text>
            </TouchableOpacity>
        </View>
    );
};
```

### 5. Integración en CarsScreen.js (Aplicación Móvil)

```javascript
import { useVehicleService } from '../hooks/useApiService';

const CarsScreen = () => {
    const { createVehicle, updateVehicle } = useVehicleService();
    
    const handleAddVehicle = async (vehicleData) => {
        try {
            await createVehicle.execute(vehicleData);
            Alert.alert('Éxito', 'Vehículo agregado exitosamente');
            fetchVehicles(); // Actualizar lista
        } catch (error) {
            Alert.alert('Error', error.message);
        }
    };
    
    const handleEditVehicle = async (vehicleId, vehicleData) => {
        try {
            const dataWithId = { ...vehicleData, vehicleId };
            await updateVehicle.execute(dataWithId);
            Alert.alert('Éxito', 'Vehículo actualizado exitosamente');
            fetchVehicles();
        } catch (error) {
            Alert.alert('Error', error.message);
        }
    };
};
```

## Patrones de Uso Recomendados

### 1. Manejo de Estados de Loading

```javascript
const MyComponent = () => {
    const { createCustomer } = useCustomerService();
    
    return (
        <Button
            disabled={createCustomer.loading}
            onPress={handleCreate}
        >
            {createCustomer.loading ? (
                <>
                    <CircularProgress size={20} />
                    <span>Creando...</span>
                </>
            ) : (
                'Crear Cliente'
            )}
        </Button>
    );
};
```

### 2. Manejo de Errores Consistente

```javascript
const handleApiCall = async (apiFunction, data, successMessage) => {
    try {
        const result = await apiFunction.execute(data);
        showSuccessNotification(successMessage);
        return result;
    } catch (error) {
        showErrorNotification(error.message);
        console.error('API Error:', error);
        throw error;
    }
};
```

### 3. Validación Antes de Enviar

```javascript
const handleSubmit = async (formData) => {
    // Validar datos antes de enviar
    const errors = validationHelpers.validateCustomerForm(formData);
    
    if (Object.keys(errors).length > 0) {
        setFormErrors(errors);
        return;
    }
    
    // Proceder con la API call
    await handleApiCall(createCustomer, formData, 'Cliente creado exitosamente');
};
```

## Migración Gradual

### Paso 1: Identificar Componentes a Migrar
1. `EditUser.js` - Actualización de usuarios
2. `CarsList.js` - Gestión de vehículos  
3. `AddBookings.js` - Creación de entregas
4. `Registration.js` - Registro de usuarios
5. `CarsScreen.js` - Gestión móvil de vehículos

### Paso 2: Implementar Gradualmente
```javascript
// Mantener funcionalidad existente como fallback
const handleUpdate = async (data) => {
    if (USE_NEW_API) {
        // Usar nuevos servicios API
        try {
            await updateCustomer.execute(data);
        } catch (error) {
            // Fallback a método anterior si falla
            dispatch(editUser(data.id, data));
        }
    } else {
        // Usar método anterior
        dispatch(editUser(data.id, data));
    }
};
```

### Paso 3: Testing y Validación
1. Probar cada endpoint individualmente
2. Validar integración con componentes existentes
3. Verificar manejo de errores
4. Confirmar compatibilidad con datos existentes

## Configuración de Entorno

### Variables de Entorno
```javascript
// .env
REACT_APP_USE_NEW_API=true
REACT_APP_API_BASE_URL=https://us-central1-balle-813e3.cloudfunctions.net
```

### Configuración Condicional
```javascript
const apiConfig = {
    useNewApi: process.env.REACT_APP_USE_NEW_API === 'true',
    baseUrl: process.env.REACT_APP_API_BASE_URL
};
```

## Testing de los Endpoints

### Usando Postman
1. Importar la colección `Apis-Art.postman_collection.json`
2. Configurar variables de entorno
3. Probar cada endpoint con datos de prueba

### Usando Jest (Testing Unitario)
```javascript
import { createCustomer } from '../services/apiService';

describe('Customer API', () => {
    test('should create customer successfully', async () => {
        const customerData = {
            firstName: 'Test',
            lastName: 'User',
            email: '<EMAIL>',
            mobile: '+523313036531',
            password: 'TestPass123'
        };
        
        const result = await createCustomer(customerData);
        expect(result.success).toBe(true);
        expect(result.userId).toBeDefined();
    });
});
```

## Monitoreo y Logging

### Logging de API Calls
```javascript
const apiRequest = async (endpoint, options) => {
    console.log(`API Call: ${endpoint}`, options);
    
    try {
        const response = await fetch(url, requestOptions);
        console.log(`API Response: ${endpoint}`, response.status);
        return response;
    } catch (error) {
        console.error(`API Error: ${endpoint}`, error);
        throw error;
    }
};
```

### Métricas de Uso
```javascript
const trackApiUsage = (endpoint, success, duration) => {
    // Enviar métricas a servicio de analytics
    analytics.track('api_call', {
        endpoint,
        success,
        duration,
        timestamp: Date.now()
    });
};
```
