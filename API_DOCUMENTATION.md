# Documentación de API - Endpoints de Usuarios

## Base URL
```
https://us-central1-balle-813e3.cloudfunctions.net
```

## Autenticación
Para los endpoints que requieren autenticación, se debe incluir el token de administrador en el header:
```
Authorization: Basic [token_base64]
```

Para obtener el token de administrador:
```
POST /generatePermanentAdminToken
Headers:
- Content-Type: application/json
Body: (vacío)

Respuesta exitosa:
{
    "success": true,
    "token": "[token_encriptado_en_base64]",
    "message": "Token permanente generado exitosamente"
}
```

## Endpoints

### 1. Verificar Existencia de Usuario
```
POST /check_user_exists
Headers:
- Content-Type: application/json
- Authorization: Basic [token_base64]

Body:
{
    "email": "<EMAIL>",
    "mobile": "+************"
}

Respuesta exitosa:
{
    "users": [
        {
            "uid": "ID_DEL_USUARIO",
            "email": "<EMAIL>",
            "phoneNumber": "+************"
        }
    ]
}
```

### 2. Actualizar Email de Usuario
```
POST /update_user_email
Headers:
- Content-Type: application/json
- Authorization: Basic [token_base64]

Body:
{
    "uid": "ID_DEL_USUARIO",
    "email": "<EMAIL>",
    "firstName": "NuevoNombre",
    "lastName": "NuevoApellido"
}

Respuesta exitosa:
{
    "success": true,
    "user": {
        "uid": "ID_DEL_USUARIO",
        "email": "<EMAIL>"
    }
}
```

### 3. Verificar Autenticación
```
POST /check_auth_exists
Headers:
- Content-Type: application/json
- Authorization: Basic [token_base64]

Body:
{
    "data": {
        "email": "<EMAIL>",
        "mobile": "+************",
        "firstName": "Nombre",
        "lastName": "Apellido",
        "usertype": "customer"
    }
}

Respuesta exitosa:
{
    "uid": "ID_DEL_USUARIO",
    "email": "<EMAIL>",
    "mobile": "+************",
    "usertype": "customer"
}
```

### 4. Actualizar Teléfono Móvil
```
POST /update_auth_mobile
Headers:
- Content-Type: application/json
- Authorization: Basic [token_base64]

Body:
{
    "uid": "ID_DEL_USUARIO",
    "mobile": "+************",
    "otp": "123456"
}

Respuesta exitosa:
{
    "success": true,
    "user": {
        "uid": "ID_DEL_USUARIO",
        "phoneNumber": "+************"
    }
}
```

### 5. Crear Nuevo Usuario
```
POST /user_signup
Headers:
- Content-Type: application/json

Body:
{
    "regData": {
        "email": "<EMAIL>",
        "mobile": "+************",
        "password": "contraseña123",
        "firstName": "Nombre",
        "lastName": "Apellido",
        "usertype": "customer",
        "signupViaReferral": "CODIGO_REFERIDO",
        "rfc": "XAXX010101000",
        "city": "Ciudad de México",
        "nombreComercial": "Mi Empresa S.A. de C.V."
    }
}

Respuesta exitosa:
{
    "uid": "ID_DEL_USUARIO"
}
```

## Notas Importantes

1.  **Tipos de Usuario (usertype)**
    -   customer
    -   driver
    -   admin
    -   fleetadmin

2.  **Formato de Teléfono Móvil**
    -   Debe incluir código de país (ejemplo: +52 para México)
    -   Formato: +\[código\_país\]\[número\]

3.  **Manejo de Errores**
    Las respuestas de error siguen este formato:
    ```json
    {
        "error": "Mensaje de error"
    }
    ```

4.  **Headers Comunes**
    -   Content-Type: application/json
    -   Authorization: Basic \[token\_base64\] (cuando se requiere)

5.  **Seguridad**
    -   Todos los endpoints que requieren autenticación deben incluir el token de administrador
    -   El token debe ser obtenido previamente usando el endpoint generatePermanentAdminToken
    -   Las contraseñas deben cumplir con los requisitos mínimos de seguridad

## Ejemplos de Uso en Postman

1.  **Configuración del Token**
    -   Crear una variable de entorno en Postman llamada `admin_token`
    -   Obtener el token usando el endpoint generatePermanentAdminToken
    -   Guardar el token en la variable de entorno

2.  **Headers Predefinidos**
    Crear un header predefinido en Postman:
    ```
    Authorization: Basic {{admin_token}}
    Content-Type: application/json
    ```

3.  **Colección de Postman**
    -   Crear una colección para todos los endpoints
    -   Usar variables de entorno para la URL base
    -   Organizar los endpoints por categorías