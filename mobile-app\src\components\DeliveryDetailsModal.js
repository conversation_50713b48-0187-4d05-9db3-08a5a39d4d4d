import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    Modal,
    TouchableOpacity,
    ScrollView,
    Alert,
    TextInput
} from 'react-native';
import { Icon } from 'react-native-elements';
import { colors } from '../common/theme';

const DeliveryDetailsModal = ({ delivery, onClose, onUpdateProducts }) => {
    const [checkedProducts, setCheckedProducts] = useState({});
    const [deliveryNotes, setDeliveryNotes] = useState('');
    const [signature, setSignature] = useState('');
    const [photoEvidence, setPhotoEvidence] = useState(null);

    // Inicializar productos cuando se abre el modal
    useEffect(() => {
        if (delivery?.products) {
            const initialState = {};
            delivery.products.forEach(product => {
                initialState[product.id] = product.delivered || false;
            });
            setCheckedProducts(initialState);
        }
    }, [delivery]);

    // Manejar check/uncheck de productos
    const handleProductCheck = (productId, isChecked) => {
        setCheckedProducts(prev => ({
            ...prev,
            [productId]: isChecked
        }));
    };

    // Marcar todos los productos
    const handleMarkAll = (selectAll) => {
        const newCheckedProducts = {};
        delivery.products.forEach(product => {
            newCheckedProducts[product.id] = selectAll;
        });
        setCheckedProducts(newCheckedProducts);
    };

    // Verificar si todos están marcados
    const allChecked = delivery?.products?.every(product =>
        checkedProducts[product.id] === true
    );

    // Guardar y cerrar
    const handleSaveAndClose = async () => {
        const allProductsDelivered = delivery.products.every(product =>
            checkedProducts[product.id] === true
        );

        if (!allProductsDelivered) {
            Alert.alert(
                'Productos Pendientes',
                '¿Estás seguro de que quieres guardar sin marcar todos los productos como entregados?',
                [
                    { text: 'Cancelar', style: 'cancel' },
                    { text: 'Guardar', onPress: saveProducts }
                ]
            );
            return;
        }

        await saveProducts();
    };

    // Guardar productos
    const saveProducts = async () => {
        try {
            const updatedProducts = delivery.products.map(product => ({
                ...product,
                delivered: checkedProducts[product.id] || false,
                deliveredAt: checkedProducts[product.id] ? new Date().getTime() : null
            }));

            const success = await onUpdateProducts(delivery.id, updatedProducts);
            
            if (success) {
                Alert.alert(
                    'Éxito',
                    `Se han actualizado ${Object.values(checkedProducts).filter(Boolean).length} de ${delivery.products.length} productos.`,
                    [{ text: 'OK', onPress: onClose }]
                );
            }
        } catch (error) {
            console.error('❌ Error guardando productos:', error);
            Alert.alert('Error', 'No se pudieron guardar los productos');
        }
    };

    if (!delivery) return null;

    const deliveredCount = Object.values(checkedProducts).filter(Boolean).length;
    const totalCount = delivery?.products?.length || 0;

    return (
        <Modal
            visible={true}
            animationType="slide"
            transparent={false}
            onRequestClose={onClose}
        >
            <View style={styles.container}>
                {/* Header */}
                <View style={styles.header}>
                    <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                        <Icon
                            name="close"
                            type="material"
                            color={colors.WHITE}
                            size={24}
                        />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>Detalles de Entrega</Text>
                    <View style={styles.headerSpacer} />
                </View>

                <ScrollView style={styles.content}>
                    {/* Información de la entrega */}
                    <View style={styles.deliveryInfo}>
                        <View style={styles.infoRow}>
                            <Icon
                                name="receipt"
                                type="material"
                                color={colors.PRIMARY}
                                size={20}
                            />
                            <Text style={styles.infoLabel}>Folio:</Text>
                            <Text style={styles.infoValue}>{delivery.folio}</Text>
                        </View>

                        <View style={styles.infoRow}>
                            <Icon
                                name="person"
                                type="material"
                                color={colors.PRIMARY}
                                size={20}
                            />
                            <Text style={styles.infoLabel}>Cliente:</Text>
                            <Text style={styles.infoValue}>{delivery.customerName}</Text>
                        </View>

                        <View style={styles.infoRow}>
                            <Icon
                                name="location-on"
                                type="material"
                                color={colors.PRIMARY}
                                size={20}
                            />
                            <Text style={styles.infoLabel}>Dirección:</Text>
                            <Text style={styles.infoValue} numberOfLines={2}>
                                {delivery.dropAddress}
                            </Text>
                        </View>
                    </View>

                    {/* Progreso */}
                    <View style={styles.progressSection}>
                        <Text style={styles.progressTitle}>
                            Progreso de Entrega
                        </Text>
                        <View style={styles.progressContainer}>
                            <View style={styles.progressBar}>
                                <View 
                                    style={[
                                        styles.progressFill,
                                        { width: `${totalCount > 0 ? (deliveredCount / totalCount) * 100 : 0}%` }
                                    ]} 
                                />
                            </View>
                            <Text style={styles.progressText}>
                                {deliveredCount}/{totalCount} productos
                            </Text>
                        </View>
                    </View>

                    {/* Controles de productos */}
                    <View style={styles.productsControls}>
                        <TouchableOpacity
                            style={styles.markAllButton}
                            onPress={() => handleMarkAll(true)}
                        >
                            <Icon
                                name="check-box"
                                type="material"
                                color={colors.PRIMARY}
                                size={20}
                            />
                            <Text style={styles.markAllText}>Marcar Todos</Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={styles.unmarkAllButton}
                            onPress={() => handleMarkAll(false)}
                        >
                            <Icon
                                name="check-box-outline-blank"
                                type="material"
                                color={colors.GREY}
                                size={20}
                            />
                            <Text style={styles.unmarkAllText}>Desmarcar Todos</Text>
                        </TouchableOpacity>
                    </View>

                    {/* Lista de productos */}
                    <View style={styles.productsSection}>
                        <Text style={styles.productsTitle}>Productos a Entregar</Text>
                        <ScrollView style={styles.productsList}>
                            {delivery.products.map((product, index) => (
                                <TouchableOpacity
                                    key={product.id}
                                    style={[
                                        styles.productItem,
                                        checkedProducts[product.id] && styles.productItemChecked
                                    ]}
                                    onPress={() => handleProductCheck(product.id, !checkedProducts[product.id])}
                                >
                                    <View style={styles.productInfo}>
                                        <Text style={styles.productName}>
                                            {product.quantity}x {product.name}
                                        </Text>
                                        <Text style={styles.productStatus}>
                                            {checkedProducts[product.id] ? 'Entregado' : 'Pendiente'}
                                        </Text>
                                    </View>
                                    <Icon
                                        name={checkedProducts[product.id] ? "check-circle" : "radio-button-unchecked"}
                                        type="material"
                                        color={checkedProducts[product.id] ? colors.GREEN : colors.GREY}
                                        size={24}
                                    />
                                </TouchableOpacity>
                            ))}
                        </ScrollView>
                    </View>

                    {/* Notas de entrega */}
                    <View style={styles.notesSection}>
                        <Text style={styles.notesTitle}>Notas de Entrega</Text>
                        <TextInput
                            style={styles.notesInput}
                            placeholder="Agregar notas sobre la entrega..."
                            value={deliveryNotes}
                            onChangeText={setDeliveryNotes}
                            multiline
                            numberOfLines={3}
                        />
                    </View>
                </ScrollView>

                {/* Footer con botones */}
                <View style={styles.footer}>
                    <TouchableOpacity
                        style={styles.cancelButton}
                        onPress={onClose}
                    >
                        <Text style={styles.cancelButtonText}>Cancelar</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[
                            styles.saveButton,
                            allChecked && styles.saveButtonComplete
                        ]}
                        onPress={handleSaveAndClose}
                    >
                        <Text style={styles.saveButtonText}>
                            {allChecked ? 'Completar Entrega' : 'Guardar'}
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.BACKGROUND,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: colors.PRIMARY,
        paddingHorizontal: 16,
        paddingVertical: 12,
        elevation: 4,
    },
    closeButton: {
        padding: 4,
    },
    headerTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: colors.WHITE,
    },
    headerSpacer: {
        width: 32,
    },
    content: {
        flex: 1,
        padding: 16,
    },
    deliveryInfo: {
        backgroundColor: colors.WHITE,
        borderRadius: 12,
        padding: 16,
        marginBottom: 16,
        elevation: 2,
    },
    infoRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
    },
    infoLabel: {
        fontSize: 14,
        fontWeight: 'bold',
        color: colors.GREY,
        marginLeft: 8,
        marginRight: 8,
        minWidth: 80,
    },
    infoValue: {
        fontSize: 14,
        color: colors.BLACK,
        flex: 1,
    },
    progressSection: {
        backgroundColor: colors.WHITE,
        borderRadius: 12,
        padding: 16,
        marginBottom: 16,
        elevation: 2,
    },
    progressTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: colors.BLACK,
        marginBottom: 12,
    },
    progressContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    progressBar: {
        flex: 1,
        height: 8,
        backgroundColor: colors.LIGHT_GREY,
        borderRadius: 4,
        marginRight: 12,
    },
    progressFill: {
        height: '100%',
        backgroundColor: colors.GREEN,
        borderRadius: 4,
    },
    progressText: {
        fontSize: 14,
        fontWeight: 'bold',
        color: colors.GREY,
        minWidth: 80,
    },
    productsControls: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 16,
    },
    markAllButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: colors.WHITE,
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 8,
        elevation: 1,
    },
    markAllText: {
        fontSize: 14,
        color: colors.PRIMARY,
        marginLeft: 4,
        fontWeight: 'bold',
    },
    unmarkAllButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: colors.WHITE,
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 8,
        elevation: 1,
    },
    unmarkAllText: {
        fontSize: 14,
        color: colors.GREY,
        marginLeft: 4,
    },
    productsSection: {
        backgroundColor: colors.WHITE,
        borderRadius: 12,
        padding: 16,
        marginBottom: 16,
        elevation: 2,
    },
    productsTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: colors.BLACK,
        marginBottom: 12,
    },
    productsList: {
        maxHeight: 300,
    },
    productItem: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingVertical: 12,
        paddingHorizontal: 8,
        borderBottomWidth: 1,
        borderBottomColor: colors.LIGHT_GREY,
    },
    productItemChecked: {
        backgroundColor: colors.LIGHT_GREEN,
        borderRadius: 8,
    },
    productInfo: {
        flex: 1,
    },
    productName: {
        fontSize: 16,
        color: colors.BLACK,
        fontWeight: '500',
    },
    productStatus: {
        fontSize: 12,
        color: colors.GREY,
        marginTop: 2,
    },
    notesSection: {
        backgroundColor: colors.WHITE,
        borderRadius: 12,
        padding: 16,
        marginBottom: 16,
        elevation: 2,
    },
    notesTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: colors.BLACK,
        marginBottom: 12,
    },
    notesInput: {
        borderWidth: 1,
        borderColor: colors.LIGHT_GREY,
        borderRadius: 8,
        padding: 12,
        fontSize: 14,
        color: colors.BLACK,
        minHeight: 80,
        textAlignVertical: 'top',
    },
    footer: {
        flexDirection: 'row',
        padding: 16,
        borderTopWidth: 1,
        borderTopColor: colors.LIGHT_GREY,
        backgroundColor: colors.WHITE,
    },
    cancelButton: {
        flex: 1,
        backgroundColor: colors.GREY,
        paddingVertical: 12,
        borderRadius: 8,
        marginRight: 8,
    },
    cancelButtonText: {
        color: colors.WHITE,
        fontSize: 16,
        fontWeight: 'bold',
        textAlign: 'center',
    },
    saveButton: {
        flex: 1,
        backgroundColor: colors.PRIMARY,
        paddingVertical: 12,
        borderRadius: 8,
        marginLeft: 8,
    },
    saveButtonComplete: {
        backgroundColor: colors.GREEN,
    },
    saveButtonText: {
        color: colors.WHITE,
        fontSize: 16,
        fontWeight: 'bold',
        textAlign: 'center',
    },
});

export default DeliveryDetailsModal; 