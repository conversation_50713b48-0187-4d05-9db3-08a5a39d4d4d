/**
 * Utilidades para gestión de entregas agrupadas
 */

import { Alert } from 'react-native';
import i18n from 'i18n-js';

const { t } = i18n;

/**
 * Estados posibles de una entrega
 */
export const DELIVERY_STATES = {
    PENDING: 'pending',
    ASSIGNED: 'assigned',
    ACCEPTED: 'accepted',
    IN_PROGRESS: 'in_progress',
    COMPLETED: 'completed',
    PARTIAL: 'partial',
    CANCELLED: 'cancelled'
};

/**
 * Estados posibles de una orden individual
 */
export const ORDER_STATES = {
    PENDING: 'pending',
    IN_PROGRESS: 'in_progress',
    COMPLETED: 'completed',
    PARTIAL: 'partial',
    CANCELLED: 'cancelled'
};

/**
 * Validar si una entrega puede ser iniciada
 */
export const canStartDelivery = (delivery) => {
    if (!delivery) return false;
    return delivery.status === DELIVERY_STATES.ACCEPTED;
};

/**
 * Validar si una orden puede ser completada
 */
export const canCompleteOrder = (order, products = []) => {
    if (!order) return false;
    
    // Verificar que la orden esté en progreso
    if (order.status !== ORDER_STATES.IN_PROGRESS) return false;
    
    // Si hay productos, verificar que al menos uno esté marcado como entregado
    if (products.length > 0) {
        return products.some(product => product.delivered && product.deliveredQuantity > 0);
    }
    
    return true;
};

/**
 * Calcular el progreso de una entrega agrupada
 */
export const calculateDeliveryProgress = (orders) => {
    if (!orders || orders.length === 0) {
        return { completed: 0, total: 0, percentage: 0 };
    }
    
    const completed = orders.filter(order => 
        order.status === ORDER_STATES.COMPLETED || order.status === ORDER_STATES.PARTIAL
    ).length;
    
    const total = orders.length;
    const percentage = Math.round((completed / total) * 100);
    
    return { completed, total, percentage };
};

/**
 * Calcular el total de productos en una entrega agrupada
 */
export const calculateTotalProducts = (orders) => {
    if (!orders || orders.length === 0) return 0;
    
    return orders.reduce((total, order) => {
        return total + (order.products?.length || 0);
    }, 0);
};

/**
 * Validar datos de verificación de entrega
 */
export const validateDeliveryVerification = (verificationData, requiresPhoto = true, requiresSignature = true) => {
    const errors = [];
    
    if (!verificationData.allProductsVerified) {
        errors.push('Debe verificar todos los productos');
    }
    
    if (!verificationData.addressConfirmed) {
        errors.push('Debe confirmar la dirección de entrega');
    }
    
    if (requiresPhoto && !verificationData.deliveryPhoto) {
        errors.push('Debe tomar una foto de la entrega');
    }
    
    if (requiresSignature && !verificationData.customerSignature) {
        errors.push('Debe obtener la firma del cliente');
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Generar resumen de entrega para mostrar al usuario
 */
export const generateDeliverySummary = (deliveryData) => {
    const { products, verification } = deliveryData;
    
    const totalProducts = products.length;
    const deliveredProducts = products.filter(p => p.delivered).length;
    const partialProducts = products.filter(p => p.delivered && p.deliveredQuantity < p.originalQuantity).length;
    
    const isPartialDelivery = partialProducts > 0 || deliveredProducts < totalProducts;
    
    return {
        totalProducts,
        deliveredProducts,
        partialProducts,
        isPartialDelivery,
        deliveryType: isPartialDelivery ? 'Entrega Parcial' : 'Entrega Completa',
        timestamp: verification.timestamp,
        hasPhoto: !!verification.deliveryPhoto,
        hasSignature: !!verification.customerSignature,
        notes: verification.deliveryNotes || ''
    };
};

/**
 * Formatear dirección para mostrar
 */
export const formatAddress = (address) => {
    if (typeof address === 'string') return address;
    if (address && address.address) return address.address;
    return 'Dirección no disponible';
};

/**
 * Formatear número de teléfono
 */
export const formatPhoneNumber = (phone) => {
    if (!phone) return '';
    
    // Remover caracteres no numéricos excepto +
    const cleaned = phone.replace(/[^\d+]/g, '');
    
    // Si empieza con +52 (México), formatear
    if (cleaned.startsWith('+52')) {
        const number = cleaned.substring(3);
        if (number.length === 10) {
            return `+52 ${number.substring(0, 2)} ${number.substring(2, 6)} ${number.substring(6)}`;
        }
    }
    
    return cleaned;
};

/**
 * Obtener color según el estado de la orden
 */
export const getOrderStatusColor = (status) => {
    switch (status) {
        case ORDER_STATES.COMPLETED:
            return '#4CAF50'; // Verde
        case ORDER_STATES.IN_PROGRESS:
            return '#2196F3'; // Azul
        case ORDER_STATES.PARTIAL:
            return '#FF9800'; // Naranja
        case ORDER_STATES.PENDING:
            return '#9E9E9E'; // Gris
        case ORDER_STATES.CANCELLED:
            return '#F44336'; // Rojo
        default:
            return '#9E9E9E';
    }
};

/**
 * Obtener icono según el estado de la orden
 */
export const getOrderStatusIcon = (status) => {
    switch (status) {
        case ORDER_STATES.COMPLETED:
            return 'check-circle';
        case ORDER_STATES.IN_PROGRESS:
            return 'access-time';
        case ORDER_STATES.PARTIAL:
            return 'warning';
        case ORDER_STATES.PENDING:
            return 'radio-button-unchecked';
        case ORDER_STATES.CANCELLED:
            return 'cancel';
        default:
            return 'radio-button-unchecked';
    }
};

/**
 * Obtener texto según el estado de la orden
 */
export const getOrderStatusText = (status) => {
    switch (status) {
        case ORDER_STATES.COMPLETED:
            return 'Completada';
        case ORDER_STATES.IN_PROGRESS:
            return 'En Progreso';
        case ORDER_STATES.PARTIAL:
            return 'Parcial';
        case ORDER_STATES.PENDING:
            return 'Pendiente';
        case ORDER_STATES.CANCELLED:
            return 'Cancelada';
        default:
            return 'Desconocido';
    }
};

/**
 * Mostrar confirmación antes de completar entrega
 */
export const showDeliveryConfirmation = (summary, onConfirm, onCancel) => {
    const message = `
Resumen de entrega:
• ${summary.deliveredProducts}/${summary.totalProducts} productos entregados
• Tipo: ${summary.deliveryType}
${summary.partialProducts > 0 ? `• ${summary.partialProducts} productos con entrega parcial` : ''}
${summary.notes ? `• Notas: ${summary.notes}` : ''}

¿Confirmar entrega?`;

    Alert.alert(
        'Confirmar Entrega',
        message,
        [
            { text: 'Cancelar', style: 'cancel', onPress: onCancel },
            { text: 'Confirmar', onPress: onConfirm }
        ]
    );
};

/**
 * Validar estructura de datos de entrega agrupada
 */
export const validateGroupedDeliveryData = (deliveryData) => {
    const errors = [];
    
    if (!deliveryData.folio) {
        errors.push('Folio es requerido');
    }
    
    if (!deliveryData.driver) {
        errors.push('Conductor es requerido');
    }
    
    if (!deliveryData.orders || deliveryData.orders.length === 0) {
        errors.push('Debe haber al menos una orden');
    }
    
    if (deliveryData.orders) {
        deliveryData.orders.forEach((order, index) => {
            if (!order.customerName) {
                errors.push(`Orden ${index + 1}: Nombre del cliente es requerido`);
            }
            
            if (!order.deliveryAddress || !order.deliveryAddress.address) {
                errors.push(`Orden ${index + 1}: Dirección de entrega es requerida`);
            }
        });
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Generar ID único para tracking
 */
export const generateTrackingId = () => {
    return `TRK-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Calcular tiempo estimado total de entrega
 */
export const calculateEstimatedDeliveryTime = (orders) => {
    if (!orders || orders.length === 0) return 0;
    
    // Tiempo base por orden (15 minutos)
    const baseTimePerOrder = 15;
    
    // Tiempo adicional por producto (2 minutos)
    const timePerProduct = 2;
    
    const totalProducts = calculateTotalProducts(orders);
    const totalTime = (orders.length * baseTimePerOrder) + (totalProducts * timePerProduct);
    
    return totalTime; // en minutos
};

/**
 * Formatear tiempo en formato legible
 */
export const formatTime = (minutes) => {
    if (minutes < 60) {
        return `${minutes} min`;
    }
    
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (remainingMinutes === 0) {
        return `${hours}h`;
    }
    
    return `${hours}h ${remainingMinutes}min`;
};
