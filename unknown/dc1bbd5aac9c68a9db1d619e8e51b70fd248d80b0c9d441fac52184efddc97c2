/**
 * Componente de ejemplo que muestra cómo usar los nuevos servicios API
 * para crear y actualizar usuarios (clientes y conductores)
 */

import React, { useState, useEffect } from 'react';
import {
    Card,
    CardContent,
    TextField,
    Button,
    Grid,
    Typography,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Alert,
    CircularProgress,
    Box
} from '@mui/material';
import { useCustomerService, useDriverService, useNotification } from '../hooks/useApiService';

const UserFormWithApi = ({ userType = 'customer', userData = null, onSuccess, onCancel }) => {
    const [formData, setFormData] = useState({
        firstName: '',
        lastName: '',
        email: '',
        mobile: '',
        password: '',
        nombreComercial: '',
        rfc: '',
        city: '',
        uid: ''
    });

    const [isEditMode, setIsEditMode] = useState(false);
    
    // Hooks para servicios API
    const { createCustomer, updateCustomer } = useCustomerService();
    const { createDriver, updateDriver } = useDriverService();
    const { notification, showSuccess, showError, clearNotification } = useNotification();

    // Determinar qué servicio usar según el tipo de usuario
    const createService = userType === 'customer' ? createCustomer : createDriver;
    const updateService = userType === 'customer' ? updateCustomer : updateDriver;

    useEffect(() => {
        if (userData) {
            setFormData({
                firstName: userData.firstName || '',
                lastName: userData.lastName || '',
                email: userData.email || '',
                mobile: userData.mobile || '',
                password: '',
                nombreComercial: userData.nombreComercial || '',
                rfc: userData.rfc || '',
                city: userData.city || '',
                uid: userData.uid || userData.id || ''
            });
            setIsEditMode(true);
        }
    }, [userData]);

    const handleInputChange = (field) => (event) => {
        setFormData(prev => ({
            ...prev,
            [field]: event.target.value
        }));
    };

    const validateForm = () => {
        const requiredFields = ['firstName', 'lastName', 'email', 'mobile'];
        if (!isEditMode) {
            requiredFields.push('password');
        }

        for (const field of requiredFields) {
            if (!formData[field].trim()) {
                showError(`El campo ${field} es requerido`);
                return false;
            }
        }

        // Validar email
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.email)) {
            showError('El formato del email no es válido');
            return false;
        }

        // Validar teléfono (debe empezar con +)
        if (!formData.mobile.startsWith('+')) {
            showError('El teléfono debe incluir el código de país (ej: +52)');
            return false;
        }

        return true;
    };

    const handleSubmit = async (event) => {
        event.preventDefault();
        clearNotification();

        if (!validateForm()) {
            return;
        }

        try {
            let result;
            
            if (isEditMode) {
                // Actualizar usuario existente
                result = await updateService.execute(formData);
                showSuccess(`${userType === 'customer' ? 'Cliente' : 'Conductor'} actualizado exitosamente`);
            } else {
                // Crear nuevo usuario
                result = await createService.execute(formData);
                showSuccess(`${userType === 'customer' ? 'Cliente' : 'Conductor'} creado exitosamente`);
            }

            if (onSuccess) {
                onSuccess(result);
            }
        } catch (error) {
            showError(error.message || 'Error al procesar la solicitud');
        }
    };

    const isLoading = createService.loading || updateService.loading;

    return (
        <Card>
            <CardContent>
                <Typography variant="h5" component="h2" gutterBottom>
                    {isEditMode ? 'Editar' : 'Crear'} {userType === 'customer' ? 'Cliente' : 'Conductor'}
                </Typography>

                {notification && (
                    <Alert 
                        severity={notification.type} 
                        onClose={clearNotification}
                        sx={{ mb: 2 }}
                    >
                        {notification.message}
                    </Alert>
                )}

                <form onSubmit={handleSubmit}>
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="Nombre"
                                value={formData.firstName}
                                onChange={handleInputChange('firstName')}
                                required
                                disabled={isLoading}
                            />
                        </Grid>
                        
                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="Apellido"
                                value={formData.lastName}
                                onChange={handleInputChange('lastName')}
                                required
                                disabled={isLoading}
                            />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="Email"
                                type="email"
                                value={formData.email}
                                onChange={handleInputChange('email')}
                                required
                                disabled={isLoading}
                            />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="Teléfono"
                                value={formData.mobile}
                                onChange={handleInputChange('mobile')}
                                placeholder="+52XXXXXXXXXX"
                                required
                                disabled={isLoading}
                            />
                        </Grid>

                        {!isEditMode && (
                            <Grid item xs={12}>
                                <TextField
                                    fullWidth
                                    label="Contraseña"
                                    type="password"
                                    value={formData.password}
                                    onChange={handleInputChange('password')}
                                    required
                                    disabled={isLoading}
                                />
                            </Grid>
                        )}

                        {userType === 'customer' && (
                            <>
                                <Grid item xs={12} sm={6}>
                                    <TextField
                                        fullWidth
                                        label="Nombre Comercial"
                                        value={formData.nombreComercial}
                                        onChange={handleInputChange('nombreComercial')}
                                        disabled={isLoading}
                                    />
                                </Grid>

                                <Grid item xs={12} sm={6}>
                                    <TextField
                                        fullWidth
                                        label="RFC"
                                        value={formData.rfc}
                                        onChange={handleInputChange('rfc')}
                                        disabled={isLoading}
                                    />
                                </Grid>
                            </>
                        )}

                        <Grid item xs={12}>
                            <TextField
                                fullWidth
                                label="Ciudad"
                                value={formData.city}
                                onChange={handleInputChange('city')}
                                disabled={isLoading}
                            />
                        </Grid>

                        <Grid item xs={12}>
                            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                                {onCancel && (
                                    <Button
                                        variant="outlined"
                                        onClick={onCancel}
                                        disabled={isLoading}
                                    >
                                        Cancelar
                                    </Button>
                                )}
                                
                                <Button
                                    type="submit"
                                    variant="contained"
                                    disabled={isLoading}
                                    startIcon={isLoading && <CircularProgress size={20} />}
                                >
                                    {isLoading 
                                        ? 'Procesando...' 
                                        : isEditMode 
                                            ? 'Actualizar' 
                                            : 'Crear'
                                    }
                                </Button>
                            </Box>
                        </Grid>
                    </Grid>
                </form>
            </CardContent>
        </Card>
    );
};

export default UserFormWithApi;
