/**
 * Script de prueba para el endpoint receiveGroupedDelivery
 * Este script envía datos de prueba al endpoint para verificar que los bookings se crean correctamente
 */

const testGroupedDelivery = async () => {
    const testData = {
        folio: 'ENT-2024-TEST-001',
        driver: 'test_driver_uid_123',
        vehicle: {
            type: 'CAMIONETA',
            plate: 'TEST-123',
            image: 'https://example.com/vehicle.jpg'
        },
        orders: [
            {
                customerId: 'test_customer_uid_456',
                customerName: 'María García Test',
                customerEmail: '<EMAIL>',
                customerPhone: '+************',
                customerToken: 'test_push_token_123',
                pickupAddress: {
                    lat: 19.4326,
                    lng: -99.1332,
                    address: 'Almacén Central - Dirección de Prueba'
                },
                deliveryAddress: {
                    lat: 19.4327,
                    lng: -99.1333,
                    address: 'Destino Final - Dirección de Prueba'
                },
                estimatedFare: 150.00,
                estimatedDistance: 5.2,
                estimatedTime: 25,
                notes: 'Entrega de prueba - manejar con cuidado',
                products: [
                    {
                        id: 'PROD-TEST-001',
                        name: 'Producto de Prueba 1',
                        quantity: 2,
                        sku: 'SKU-TEST-001',
                        description: 'Descripción del producto de prueba'
                    },
                    {
                        id: 'PROD-TEST-002',
                        name: 'Producto de Prueba 2',
                        quantity: 1,
                        sku: 'SKU-TEST-002',
                        description: 'Segundo producto de prueba'
                    }
                ]
            },
            {
                customerId: 'test_customer_uid_789',
                customerName: 'Juan Pérez Test',
                customerEmail: '<EMAIL>',
                customerPhone: '+************',
                customerToken: 'test_push_token_456',
                pickupAddress: {
                    lat: 19.4328,
                    lng: -99.1334,
                    address: 'Segundo Punto de Recogida'
                },
                deliveryAddress: {
                    lat: 19.4329,
                    lng: -99.1335,
                    address: 'Segundo Destino Final'
                },
                estimatedFare: 200.00,
                estimatedDistance: 7.8,
                estimatedTime: 35,
                notes: 'Segunda entrega de prueba',
                products: [
                    {
                        id: 'PROD-TEST-003',
                        name: 'Producto de Prueba 3',
                        quantity: 3,
                        sku: 'SKU-TEST-003',
                        description: 'Tercer producto de prueba'
                    }
                ]
            }
        ],
        estimatedRoute: [
            { lat: 19.4326, lng: -99.1332 },
            { lat: 19.4327, lng: -99.1333 },
            { lat: 19.4328, lng: -99.1334 },
            { lat: 19.4329, lng: -99.1335 }
        ],
        totalDistance: 13.0,
        totalEstimatedTime: 60
    };

    try {
        console.log('Enviando datos de prueba al endpoint...');
        console.log('Datos:', JSON.stringify(testData, null, 2));
        
        const response = await fetch('https://us-central1-balle-813e3.cloudfunctions.net/receiveGroupedDelivery', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(testData)
        });

        const result = await response.json();
        
        console.log('\n=== RESULTADO DE LA PRUEBA ===');
        console.log('Status:', response.status);
        console.log('Respuesta:', JSON.stringify(result, null, 2));
        
        if (result.success) {
            console.log('\n✅ ÉXITO: La entrega agrupada se creó correctamente');
            console.log(`📦 Delivery ID: ${result.deliveryId}`);
            console.log(`📋 Folio: ${result.folio}`);
            console.log(`📊 Órdenes creadas: ${result.ordersCount}`);
            console.log(`📝 Bookings creados: ${result.bookingsCreated}`);
            
            console.log('\n📋 VERIFICACIONES RECOMENDADAS:');
            console.log('1. Revisar la tabla "bookings" en Firebase para ver los nuevos registros');
            console.log('2. Verificar que los bookings tengan los campos: groupedDeliveryId, orderIndex, folio, products, deliveryType');
            console.log('3. Comprobar que aparezcan en el historial de bookings de la aplicación web');
            console.log('4. Verificar que el conductor reciba la notificación push');
        } else {
            console.log('\n❌ ERROR: La prueba falló');
            console.log('Error:', result.error);
        }
        
    } catch (error) {
        console.error('\n💥 ERROR DE CONEXIÓN:', error.message);
        console.log('\nVerificar:');
        console.log('- Conexión a internet');
        console.log('- URL del endpoint');
        console.log('- Configuración de CORS');
    }
};

// Ejecutar la prueba si el archivo se ejecuta directamente
if (typeof window === 'undefined') {
    // Entorno Node.js
    const fetch = require('node-fetch');
    testGroupedDelivery();
} else {
    // Entorno del navegador
    console.log('Para ejecutar la prueba en el navegador, llama a testGroupedDelivery()');
}

// Exportar para uso en otros archivos
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testGroupedDelivery };
}
