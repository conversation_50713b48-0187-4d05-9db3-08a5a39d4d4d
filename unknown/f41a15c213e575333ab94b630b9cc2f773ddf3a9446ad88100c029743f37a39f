import React from 'react';
import {
    View,
    Text,
    StyleSheet,
    Modal,
    TouchableOpacity,
    ScrollView,
    FlatList
} from 'react-native';
import { Icon } from 'react-native-elements';
import { colors } from '../common/theme';
import { fonts } from '../common/font';
import i18n from 'i18n-js';

export default function DeliveryProductsModal({ 
    visible, 
    onClose, 
    bookingData,
    onStartDelivery 
}) {
    const { t } = i18n;
    const isRTL = i18n.locale.indexOf('he') === 0 || i18n.locale.indexOf('ar') === 0;
    
    const products = bookingData?.products || [];
    const isGroupedDelivery = bookingData?.deliveryType === 'grouped_delivery';
    
    const getProductIcon = (category) => {
        switch (category?.toLowerCase()) {
            case 'art':
            case 'artwork':
                return 'palette';
            case 'fragile':
            case 'glass':
                return 'warning';
            case 'document':
                return 'description';
            case 'electronics':
                return 'devices';
            default:
                return 'inventory';
        }
    };

    const getProductColor = (category) => {
        switch (category?.toLowerCase()) {
            case 'art':
            case 'artwork':
                return colors.PURPLE;
            case 'fragile':
            case 'glass':
                return colors.RED;
            case 'document':
                return colors.BLUE;
            case 'electronics':
                return colors.GREEN;
            default:
                return colors.GREY;
        }
    };

    const renderProductItem = ({ item, index }) => (
        <View style={styles.productCard}>
            <View style={[styles.productHeader, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                <View style={[styles.iconContainer, { backgroundColor: getProductColor(item.category) }]}>
                    <Icon
                        name={getProductIcon(item.category)}
                        type="material"
                        color={colors.WHITE}
                        size={20}
                    />
                </View>
                <View style={styles.productInfo}>
                    <Text style={[styles.productName, { textAlign: isRTL ? 'right' : 'left' }]}>
                        {item.name || `Product ${index + 1}`}
                    </Text>
                    {item.sku && (
                        <Text style={[styles.productSku, { textAlign: isRTL ? 'right' : 'left' }]}>
                            SKU: {item.sku}
                        </Text>
                    )}
                </View>
                <View style={styles.quantityBadge}>
                    <Text style={styles.quantityText}>{item.quantity || 1}</Text>
                </View>
            </View>

            <View style={styles.productDetails}>
                {item.weight && (
                    <View style={[styles.detailRow, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                        <Icon name="fitness-center" type="material" size={16} color={colors.GREY} />
                        <Text style={styles.detailText}>{item.weight} kg</Text>
                    </View>
                )}
                
                {item.dimensions && (
                    <View style={[styles.detailRow, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                        <Icon name="straighten" type="material" size={16} color={colors.GREY} />
                        <Text style={styles.detailText}>{item.dimensions}</Text>
                    </View>
                )}

                {item.fragile && (
                    <View style={[styles.detailRow, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                        <Icon name="warning" type="material" size={16} color={colors.RED} />
                        <Text style={[styles.detailText, { color: colors.RED }]}>Frágil</Text>
                    </View>
                )}

                {item.requiresSignature && (
                    <View style={[styles.detailRow, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                        <Icon name="edit" type="material" size={16} color={colors.BLUE} />
                        <Text style={[styles.detailText, { color: colors.BLUE }]}>Requiere firma</Text>
                    </View>
                )}
            </View>

            {item.specialInstructions && (
                <View style={styles.instructionsContainer}>
                    <Text style={[styles.instructionsLabel, { textAlign: isRTL ? 'right' : 'left' }]}>Instrucciones especiales:</Text>
                    <Text style={[styles.instructionsText, { textAlign: isRTL ? 'right' : 'left' }]}>{item.specialInstructions}</Text>
                </View>
            )}
        </View>
    );

    const renderDeliveryInfo = () => (
        <View style={styles.deliveryInfo}>
            <Text style={[styles.deliveryTitle, { textAlign: isRTL ? 'right' : 'left' }]}> {bookingData?.customer_name || 'Cliente'} </Text>
            <Text style={[styles.deliveryAddress, { textAlign: isRTL ? 'right' : 'left' }]}> {bookingData?.drop?.add || 'Dirección de entrega'} </Text>
            {isGroupedDelivery && bookingData?.folio && (
                <Text style={[styles.folioText, { textAlign: isRTL ? 'right' : 'left' }]}>Folio: {bookingData.folio}</Text>
            )}
        </View>
    );

    const renderSummary = () => {
        const totalItems = products.reduce((sum, product) => sum + (parseInt(product.quantity) || 1), 0);
        const totalWeight = products.reduce((sum, product) => sum + (parseFloat(product.weight) || 0) * (parseInt(product.quantity) || 1), 0);
        
        return (
            <View style={styles.summaryContainer}>
                <View style={styles.summaryRow}>
                    <Text style={styles.summaryLabel}>Total de artículos:</Text>
                    <Text style={styles.summaryValue}>{totalItems}</Text>
                </View>
                {totalWeight > 0 && (
                    <View style={styles.summaryRow}>
                        <Text style={styles.summaryLabel}>Peso total:</Text>
                        <Text style={styles.summaryValue}>{totalWeight.toFixed(2)} kg</Text>
                    </View>
                )}
            </View>
        );
    };

    if (products.length === 0) {
        return null;
    }

    return (
        <Modal
            visible={visible}
            animationType="slide"
            transparent={false}
            onRequestClose={onClose}
        >
            <View style={styles.container}>
                <View style={[styles.header, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                    <TouchableOpacity onPress={onClose} style={styles.backButton}>
                        <Icon
                            name={isRTL ? "arrow-forward" : "arrow-back"}
                            type="material"
                            color={colors.WHITE}
                            size={24}
                        />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>Productos de la entrega</Text>
                </View>

                <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
                    {renderDeliveryInfo()}
                    {renderSummary()}
                    
                    <View style={styles.productsSection}>
                        <Text style={[styles.sectionTitle, { textAlign: isRTL ? 'right' : 'left' }]}>
                            Productos a Entregar
                        </Text>
                        <FlatList
                            data={products}
                            renderItem={renderProductItem}
                            keyExtractor={(item, index) => `product-${index}`}
                            scrollEnabled={false}
                            showsVerticalScrollIndicator={false}
                        />
                    </View>
                </ScrollView>

                <View style={styles.footer}>
                    <TouchableOpacity
                        style={styles.startButton}
                        onPress={() => {
                            onStartDelivery && onStartDelivery();
                            onClose();
                        }}
                    >
                        <Icon name="local-shipping" type="material" color={colors.WHITE} size={24} />
                        <Text style={styles.startButtonText}>Iniciar Entrega</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </Modal>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.WHITE,
    },
    header: {
        backgroundColor: colors.HEADER,
        paddingTop: 50,
        paddingBottom: 15,
        paddingHorizontal: 20,
        alignItems: 'center',
    },
    backButton: {
        position: 'absolute',
        left: 20,
        top: 55,
    },
    headerTitle: {
        color: colors.WHITE,
        fontSize: 18,
        fontFamily: fonts.Bold,
    },
    content: {
        flex: 1,
        paddingHorizontal: 20,
    },
    deliveryInfo: {
        backgroundColor: colors.LIGHT_GREY,
        padding: 15,
        borderRadius: 10,
        marginVertical: 15,
    },
    deliveryTitle: {
        fontSize: 16,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginBottom: 5,
    },
    deliveryAddress: {
        fontSize: 14,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        marginBottom: 5,
    },
    folioText: {
        fontSize: 14,
        fontFamily: fonts.Bold,
        color: colors.BLUE,
    },
    summaryContainer: {
        backgroundColor: colors.LIGHT_GREY,
        padding: 15,
        borderRadius: 10,
        marginBottom: 15,
    },
    summaryRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginVertical: 2,
    },
    summaryLabel: {
        fontSize: 14,
        fontFamily: fonts.Regular,
        color: colors.GREY,
    },
    summaryValue: {
        fontSize: 14,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
    },
    productsSection: {
        marginBottom: 20,
    },
    sectionTitle: {
        fontSize: 16,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginBottom: 15,
    },
    productCard: {
        backgroundColor: colors.WHITE,
        borderRadius: 10,
        padding: 15,
        marginVertical: 5,
        elevation: 3,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    productHeader: {
        alignItems: 'center',
        marginBottom: 10,
    },
    iconContainer: {
        width: 40,
        height: 40,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 10,
    },
    productInfo: {
        flex: 1,
    },
    productName: {
        fontSize: 16,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
    },
    productSku: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        marginTop: 2,
    },
    quantityBadge: {
        backgroundColor: colors.BLUE,
        borderRadius: 15,
        paddingHorizontal: 8,
        paddingVertical: 4,
        minWidth: 30,
        alignItems: 'center',
    },
    quantityText: {
        color: colors.WHITE,
        fontSize: 12,
        fontFamily: fonts.Bold,
    },
    productDetails: {
        marginBottom: 10,
    },
    detailRow: {
        alignItems: 'center',
        marginVertical: 2,
    },
    detailText: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        marginLeft: 5,
    },
    instructionsContainer: {
        backgroundColor: colors.LIGHT_YELLOW,
        padding: 10,
        borderRadius: 5,
        borderLeftWidth: 3,
        borderLeftColor: colors.YELLOW,
    },
    instructionsLabel: {
        fontSize: 12,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginBottom: 5,
    },
    instructionsText: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.BLACK,
    },
    footer: {
        padding: 20,
        backgroundColor: colors.WHITE,
        borderTopWidth: 1,
        borderTopColor: colors.LIGHT_GREY,
    },
    startButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.GREEN,
        padding: 15,
        borderRadius: 25,
    },
    startButtonText: {
        color: colors.WHITE,
        fontSize: 16,
        fontFamily: fonts.Bold,
        marginLeft: 10,
    },
});
