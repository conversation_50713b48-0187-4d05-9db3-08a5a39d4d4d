const admin = require('firebase-admin');

// Configuración de Firebase Admin
const serviceAccount = require('./balle-app-fd8e6-firebase-adminsdk-yusmn-a5e07904f4.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: "https://balle-813e3-default-rtdb.firebaseio.com"
});

const db = admin.database();

async function testBookingCreation() {
  console.log('🔍 Iniciando prueba de creación de booking...\n');

  try {
    // 1. Verificar configuración de Firebase
    console.log('1. Verificando configuración de Firebase...');
    const settingsRef = db.ref('settings');
    const settingsSnapshot = await settingsRef.once('value');
    
    if (settingsSnapshot.exists()) {
      console.log('✅ Configuración de Firebase encontrada');
      const settings = settingsSnapshot.val();
      console.log('   - Configuraciones disponibles:', Object.keys(settings));
    } else {
      console.log('❌ No se encontró configuración de Firebase');
      return;
    }

    // 2. Verificar usuarios existentes
    console.log('\n2. Verificando usuarios existentes...');
    const usersRef = db.ref('users');
    const usersSnapshot = await usersRef.once('value');
    
    if (usersSnapshot.exists()) {
      const users = usersSnapshot.val();
      const userKeys = Object.keys(users);
      console.log(`✅ Se encontraron ${userKeys.length} usuarios`);
      
      // Buscar usuarios por tipo
      const customers = Object.values(users).filter(user => user.usertype === 'customer');
      const drivers = Object.values(users).filter(user => user.usertype === 'driver');
      const admins = Object.values(users).filter(user => user.usertype === 'admin');
      
      console.log(`   - Clientes: ${customers.length}`);
      console.log(`   - Conductores: ${drivers.length}`);
      console.log(`   - Administradores: ${admins.length}`);
      
      if (customers.length === 0) {
        console.log('⚠️  No hay clientes registrados');
      }
    } else {
      console.log('❌ No se encontraron usuarios');
    }

    // 3. Verificar tipos de vehículos
    console.log('\n3. Verificando tipos de vehículos...');
    const carTypesRef = db.ref('cartypes');
    const carTypesSnapshot = await carTypesRef.once('value');
    
    if (carTypesSnapshot.exists()) {
      const carTypes = carTypesSnapshot.val();
      const carTypeKeys = Object.keys(carTypes);
      console.log(`✅ Se encontraron ${carTypeKeys.length} tipos de vehículos`);
      console.log('   - Tipos disponibles:', carTypeKeys);
    } else {
      console.log('❌ No se encontraron tipos de vehículos');
    }

    // 4. Verificar bookings existentes
    console.log('\n4. Verificando bookings existentes...');
    const bookingsRef = db.ref('bookings');
    const bookingsSnapshot = await bookingsRef.once('value');
    
    if (bookingsSnapshot.exists()) {
      const bookings = bookingsSnapshot.val();
      const bookingKeys = Object.keys(bookings);
      console.log(`✅ Se encontraron ${bookingKeys.length} bookings`);
      
      // Mostrar algunos ejemplos
      const sampleBookings = Object.values(bookings).slice(0, 3);
      sampleBookings.forEach((booking, index) => {
        console.log(`   - Booking ${index + 1}: ${booking.customer_name} - ${booking.status}`);
      });
    } else {
      console.log('ℹ️  No hay bookings existentes');
    }

    // 5. Probar creación de booking de prueba
    console.log('\n5. Probando creación de booking de prueba...');
    
    // Crear un booking de prueba
    const testBooking = {
      carType: 'SEDAN',
      carImage: 'https://example.com/car.png',
      customer: 'test-customer-id',
      commission_type: 'fixed',
      commission_rate: 10,
      reference: 'TEST123',
      customer_email: '<EMAIL>',
      customer_name: 'Cliente de Prueba',
      customer_contact: '+1234567890',
      customer_token: 'test-token',
      customer_image: '',
      drop: {
        lat: 19.4326,
        lng: -99.1332,
        add: 'Ciudad de México, México'
      },
      pickup: {
        lat: 19.4326,
        lng: -99.1332,
        add: 'Ciudad de México, México'
      },
      estimate: 100,
      estimateDistance: 5,
      distance: 5,
      estimateTime: 15,
      status: 'NEW',
      bookLater: false,
      tripdate: Date.now(),
      bookingDate: Date.now(),
      otp: false,
      booking_type_admin: false,
      coords: [
        {latitude: 19.4326, longitude: -99.1332},
        {latitude: 19.4326, longitude: -99.1332}
      ],
      waypoints: null,
      roundTrip: false,
      tripInstructions: 'Prueba de booking',
      trip_cost: 100,
      convenience_fees: 10,
      driver_share: 90,
      payment_mode: 'cash',
      fleetadmin: null,
      fleet_admin_comission: null,
      booking_from_web: false,
      deliveryWithBid: false
    };

    const newBookingRef = db.ref('bookings').push();
    await newBookingRef.set(testBooking);
    
    console.log('✅ Booking de prueba creado exitosamente');
    console.log(`   - ID del booking: ${newBookingRef.key}`);
    
    // Limpiar el booking de prueba
    await newBookingRef.remove();
    console.log('✅ Booking de prueba eliminado');

    console.log('\n🎉 Todas las pruebas completadas exitosamente!');
    console.log('\n📋 Resumen de verificación:');
    console.log('   - Firebase configurado correctamente');
    console.log('   - Base de datos accesible');
    console.log('   - Permisos de escritura funcionando');
    console.log('   - Estructura de datos correcta');

  } catch (error) {
    console.error('\n❌ Error durante la prueba:', error);
    console.log('\n🔧 Posibles soluciones:');
    console.log('   1. Verificar que Firebase esté configurado correctamente');
    console.log('   2. Verificar las reglas de seguridad de la base de datos');
    console.log('   3. Verificar que el archivo de credenciales sea válido');
    console.log('   4. Verificar la conectividad a internet');
  }
}

// Ejecutar la prueba
testBookingCreation().then(() => {
  console.log('\n🏁 Prueba finalizada');
  process.exit(0);
}).catch((error) => {
  console.error('Error fatal:', error);
  process.exit(1);
}); 