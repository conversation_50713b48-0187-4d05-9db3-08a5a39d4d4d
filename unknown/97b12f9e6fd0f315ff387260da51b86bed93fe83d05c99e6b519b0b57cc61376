# Integración de Entregas Agrupadas con Sistema de Bookings

## Resumen de Cambios

Se ha modificado el endpoint `receiveGroupedDelivery` para que las entregas agrupadas también se guarden en la tabla `bookings`, permitiendo que sigan el proceso normal de entrega del sistema.

## Cambios Implementados

### 1. Nueva Función de Mapeo: `mapOrderToBooking`

Se agregó una función auxiliar que convierte los datos de una orden de entrega agrupada al formato estándar de booking:

```javascript
const mapOrderToBooking = (order, deliveryData, orderIndex, deliveryId) => {
    // Mapea todos los campos necesarios para crear un booking válido
    // Incluye campos específicos para entregas agrupadas
}
```

### 2. Campos Específicos para Entregas Agrupadas

Los bookings creados desde entregas agrupadas incluyen estos campos adicionales:

- **`groupedDeliveryId`**: ID de la entrega agrupada padre
- **`orderIndex`**: Posición de la orden dentro de la entrega agrupada
- **`folio`**: Folio de la entrega agrupada
- **`products`**: Array de productos de la orden
- **`deliveryType`**: Identificador con valor "grouped_delivery"
- **`notes`**: Notas específicas de la orden
- **`orderId`**: Vinculación con el registro en la tabla orders

### 3. Modificaciones en el Endpoint

El endpoint `receiveGroupedDelivery` ahora:

1. **Mantiene** toda la funcionalidad existente:
   - Crea registro en `groupedDeliveries`
   - Crea registros individuales en `orders`
   - Envía notificaciones push al conductor

2. **Agrega** nueva funcionalidad:
   - Crea bookings individuales para cada orden
   - Vincula bookings con orders y groupedDeliveries
   - Incluye información de bookings en la respuesta

## Estructura de Datos

### Booking Creado desde Entrega Agrupada

```javascript
{
    // Campos estándar de booking
    reference: "GD123ABC",
    customer: "customer_uid",
    customer_name: "Nombre Cliente",
    customer_email: "<EMAIL>",
    pickup: { lat: 19.4326, lng: -99.1332, add: "Dirección recogida" },
    drop: { lat: 19.4327, lng: -99.1333, add: "Dirección entrega" },
    status: "NEW",
    driver: "driver_uid",
    carType: "CAMIONETA",
    
    // Campos específicos para entregas agrupadas
    groupedDeliveryId: "grouped_delivery_id",
    orderIndex: 0,
    folio: "ENT-2024-001",
    products: [
        {
            id: "PROD-001",
            name: "Producto 1",
            quantity: 2,
            sku: "SKU-001"
        }
    ],
    deliveryType: "grouped_delivery",
    orderId: "order_id",
    
    // Campos de costos
    estimate: 150.00,
    trip_cost: 150.00,
    payableAmount: 150.00,
    payment_mode: "cash",
    
    // Otros campos requeridos
    booking_type_admin: true,
    booking_from_web: true,
    // ... más campos según estructura estándar
}
```

## Beneficios de la Integración

### 1. Visibilidad Completa
- Las entregas agrupadas aparecen en el historial de bookings
- Se pueden rastrear como cualquier booking normal
- Reportes y estadísticas incluyen entregas agrupadas

### 2. Flujo de Estados Unificado
- Las entregas siguen los estados estándar: NEW → ACCEPTED → ARRIVED → STARTED → COMPLETE
- Se pueden aplicar las mismas reglas de negocio
- Notificaciones y actualizaciones automáticas

### 3. Compatibilidad con Sistema Existente
- No se rompe funcionalidad existente
- Las entregas agrupadas se mantienen en sus tablas específicas
- Doble registro permite flexibilidad

## Verificación de la Implementación

### 1. Probar el Endpoint

Usar el archivo `test_grouped_delivery.js` para enviar datos de prueba:

```bash
node test_grouped_delivery.js
```

### 2. Verificar en Firebase

Después de enviar una entrega agrupada, verificar:

1. **Tabla `groupedDeliveries`**: Debe tener el registro principal
2. **Tabla `orders`**: Debe tener registros individuales con `bookingId`
3. **Tabla `bookings`**: Debe tener bookings con campos específicos de entrega agrupada

### 3. Verificar en la Aplicación Web

1. Ir a "Historial de Bookings"
2. Buscar bookings con `deliveryType: "grouped_delivery"`
3. Verificar que muestren información correcta
4. Comprobar que se puedan actualizar estados normalmente

## Campos de Vinculación

Para mantener la integridad referencial:

- **`groupedDeliveries.id`** ← **`bookings.groupedDeliveryId`**
- **`orders.id`** ← **`bookings.orderId`**
- **`bookings.id`** ← **`orders.bookingId`**

## Consideraciones Importantes

### 1. Estados de Booking
Los bookings se crean con estado "NEW" y deben seguir el flujo normal:
- NEW → ACCEPTED (conductor acepta)
- ACCEPTED → ARRIVED (conductor llega al pickup)
- ARRIVED → STARTED (inicia el viaje)
- STARTED → COMPLETE (completa la entrega)

### 2. Pagos
Por defecto se configuran como:
- `payment_mode: "cash"`
- `prepaid: false`
- `payableAmount: estimatedFare`

### 3. Notificaciones
El conductor recibe una notificación de la entrega agrupada, pero cada booking individual puede generar notificaciones adicionales según el flujo normal.

## Próximos Pasos Recomendados

1. **Probar la integración** con datos reales
2. **Verificar la interfaz web** para asegurar que los bookings aparezcan correctamente
3. **Ajustar campos** si es necesario según los requerimientos específicos
4. **Implementar validaciones adicionales** si se requieren
5. **Documentar el proceso** para el equipo de desarrollo
