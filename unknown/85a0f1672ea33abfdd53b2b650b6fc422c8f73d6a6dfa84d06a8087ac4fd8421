{"info": {"_postman_id": "f9ec0635-fdc5-4ae6-9541-e21b72d822ad", "name": "Apis-Art", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "43474355"}, "item": [{"name": "Crear cliente", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"mobile\": \"+523313036531\",\r\n    \"password\": \"Seguridad2024\",\r\n    \"firstName\": \"Test2\",\r\n    \"lastName\": \"Ape2\",\r\n    \"nombreComercial\": \"Mi Empresa 2 S.A. de C.V.\",\r\n    \"rfc\": \"XAXX010101000\",\r\n    \"city\": \"Guadalajara\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://us-central1-balle-813e3.cloudfunctions.net/createUser", "protocol": "https", "host": ["us-central1-balle-813e3", "cloudfunctions", "net"], "path": ["createUser"]}}, "response": []}, {"name": "Actualizar cliente", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"uid\": \"saWRbrfvJRawpH3ccXCvAhdCbAu2\",\r\n    \"firstName\": \"<PERSON>\",\r\n    \"lastName\": \"<PERSON>\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"mobile\": \"+************\",\r\n    \"nombreComercial\": \"Nueva Empresa S.A. de C.V.\",\r\n    \"rfc\": \"XAXX010101000\",\r\n    \"city\": \"Guadalajara\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://us-central1-balle-813e3.cloudfunctions.net/updateCustomer", "protocol": "https", "host": ["us-central1-balle-813e3", "cloudfunctions", "net"], "path": ["updateCustomer"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> conductor", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"firstName\": \"Conductor3\",\r\n    \"lastName\": \"prueba3\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"mobile\": \"+************\",\r\n    \"password\": \"Seguridad2024\", // Opcional, si no se proporciona se generará una aleatoria\r\n    \"hireDate\": 1712000000000 // Opcional, timestamp en milisegundos. Si no se proporciona, se usará la fecha actual\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://us-central1-balle-813e3.cloudfunctions.net/createDriver", "protocol": "https", "host": ["us-central1-balle-813e3", "cloudfunctions", "net"], "path": ["createDriver"]}}, "response": []}, {"name": "Actualizar conductor", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"uid\": \"Nb4YRLywQNSKZt0uciRDnrZQ4HF3\",\r\n    \"firstName\": \"Conductor1\",\r\n    \"lastName\": \"prueba1\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"mobile\": \"+************\"\r\n\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://us-central1-balle-813e3.cloudfunctions.net/updateDriver", "protocol": "https", "host": ["us-central1-balle-813e3", "cloudfunctions", "net"], "path": ["updateDriver"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "   {\r\n       \"vehicleNumber\": \"ABC123456\",\r\n       \"vehicleMake\": \"Ferrari\",\r\n       \"vehicleModel\": \"F2\",\r\n       \"axles\": 2,\r\n       \"capacity\": 40000,\r\n       \"carType\": \"Auto Deportivo\",\r\n       \"vehicleColor\": \"Rojo\",\r\n       \"vehicleYear\": \"2024\",\r\n       \"stateLicensePlate\": \"XYZ2025\"\r\n   }"}, "url": {"raw": "https://us-central1-balle-813e3.cloudfunctions.net/createVehicle", "protocol": "https", "host": ["us-central1-balle-813e3", "cloudfunctions", "net"], "path": ["createVehicle"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"vehicleId\": \"veh_112233\",\r\n  \"vehicleNumber\": \"TRK-0456\",\r\n  \"vehicleMake\": \"Ford\",\r\n  \"vehicleModel\": \"F-150\",\r\n  \"carType\": \"CAMIONETA\",\r\n  \"car_image\": \"https://example.com/images/f150.jpg\",\r\n  \"other_info\": \"Camioneta usada para transporte de herramientas\",\r\n  \"active\": true,\r\n  \"approved\": true,\r\n  \"driver\": \"IkO5kdHJhCZxzXlIjPGvPvhe4B33\",\r\n  \"unitNumber\": \"UN1234\",\r\n  \"stateLicensePlate\": \"ABC1234\",\r\n  \"vehicleColor\": \"Blanco\",\r\n  \"vehicleYear\": \"2022\",\r\n  \"vehicleCategory\": \"Comercial\",\r\n  \"federalLicensePlate\": \"FED45678\",\r\n  \"vehicleClass\": \"Clase B\",\r\n  \"vehicleType\": \"Camioneta\",\r\n  \"vehicleNotes\": \"Revisión mecánica el 15 de julio\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://us-central1-balle-813e3.cloudfunctions.net/updateVehicle", "protocol": "https", "host": ["us-central1-balle-813e3", "cloudfunctions", "net"], "path": ["updateVehicle"]}}, "response": []}, {"name": "Crear Entrega Agrupada s/f", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"folio\": \"ENT-2024-001\",\r\n  \"driver\": \"-OSgEoZzTKd3WlZ-xRyY\",\r\n  \"vehicle\": {\r\n    \"type\": \"CAMIONETA\",\r\n    \"image\": \"https://example.com/truck.png\",\r\n    \"plate\": \"ABC-123\"\r\n  },\r\n  \"orders\": [\r\n    {\r\n      \"customerId\": \"VeSkwck41kXpdJIoDQH4sMjYror1\",\r\n      \"customerName\": \"Jonathan Ballesteros\",\r\n      \"customerEmail\": \"<EMAIL>\",\r\n      \"customerPhone\": \"+523313036516\",\r\n      \"customerToken\": \"push_token_123\",\r\n      \"pickupAddress\": {\r\n        \"lat\": 19.4326,\r\n        \"lng\": -99.1332,\r\n        \"address\": \"Almacén Central, CDMX\"\r\n      },\r\n      \"deliveryAddress\": {\r\n        \"lat\": 19.4327,\r\n        \"lng\": -99.1333,\r\n        \"address\": \"Av. Reforma 123, CDMX\"\r\n      },\r\n      \"estimatedFare\": 150.00,\r\n      \"estimatedDistance\": 5.2,\r\n      \"estimatedTime\": 15,\r\n      \"notes\": \"Entregar antes de las 2 PM\",\r\n      \"products\": [\r\n        {\r\n          \"id\": \"PROD-001\",\r\n          \"name\": \"Laptop Dell XPS 13\",\r\n          \"quantity\": 1,\r\n          \"sku\": \"DELL-XPS-13-001\"\r\n        },\r\n        {\r\n          \"id\": \"PROD-002\", \r\n          \"name\": \"Mouse inalámbrico\",\r\n          \"quantity\": 2,\r\n          \"sku\": \"MOUSE-WIRELESS-002\"\r\n        }\r\n      ]\r\n    },\r\n    {\r\n      \"customerId\": \"sIsYF2NrKNeXOjhQ65W8sRiKsjD3\",\r\n      \"customerName\": \"Test2 Ape2\",\r\n      \"customerEmail\": \"<EMAIL>\", \r\n      \"customerPhone\": \"+523313036531\",\r\n      \"customerToken\": \"push_token_456\",\r\n      \"pickupAddress\": {\r\n        \"lat\": 19.4326,\r\n        \"lng\": -99.1332,\r\n        \"address\": \"Almacén Central, CDMX\"\r\n      },\r\n      \"deliveryAddress\": {\r\n        \"lat\": 19.4328,\r\n        \"lng\": -99.1334,\r\n        \"address\": \"Insurgentes Sur 456, CDMX\"\r\n      },\r\n      \"estimatedFare\": 200.00,\r\n      \"estimatedDistance\": 8.1,\r\n      \"estimatedTime\": 20,\r\n      \"notes\": \"Llamar antes de llegar\",\r\n      \"products\": [\r\n        {\r\n          \"id\": \"PROD-003\",\r\n          \"name\": \"Monitor 24 pulgadas\",\r\n          \"quantity\": 1,\r\n          \"sku\": \"MON-24-003\"\r\n        }\r\n      ]\r\n    }\r\n  ],\r\n  \"estimatedRoute\": [\r\n    {\"lat\": 19.4326, \"lng\": -99.1332},\r\n    {\"lat\": 19.4327, \"lng\": -99.1333},\r\n    {\"lat\": 19.4328, \"lng\": -99.1334}\r\n  ],\r\n  \"totalDistance\": 13.3,\r\n  \"totalEstimatedTime\": 35\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://us-central1-balle-813e3.cloudfunctions.net/receiveGroupedDelivery", "protocol": "https", "host": ["us-central1-balle-813e3", "cloudfunctions", "net"], "path": ["receiveGroupedDelivery"]}}, "response": []}]}