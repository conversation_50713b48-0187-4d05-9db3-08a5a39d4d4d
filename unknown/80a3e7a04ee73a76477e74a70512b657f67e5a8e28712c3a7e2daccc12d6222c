# Documentación de Endpoints API

## Base URL
```
https://us-central1-balle-813e3.cloudfunctions.net
```

## Endpoints

### 1. <PERSON><PERSON><PERSON>
**Endpoint:** `/createUser`  
**Método:** POST  
**Descripción:** Crea un nuevo usuario en el sistema.

**Headers:**
```
Content-Type: application/json
```

**Body:**
```json
{
    "email": "string",
    "password": "string",
    "displayName": "string",
    "phoneNumber": "string",
    "role": "string"
}
```

### 2. Actualizar Cliente
**Endpoint:** `/updateCustomer`  
**Método:** PUT  
**Descripción:** Actualiza la información de un cliente existente.

**Headers:**
```
Content-Type: application/json
```

**Body:**
```json
{
    "uid": "string",
    "name": "string",
    "email": "string",
    "phone": "string",
    "address": "string"
}
```

### 3. Crear Conductor
**Endpoint:** `/createDriver`  
**Método:** POST  
**Descripción:** Registra un nuevo conductor en el sistema.

**Headers:**
```
Content-Type: application/json
```

**Body:**
```json
{
    "name": "string",
    "email": "string",
    "phone": "string",
    "licenseNumber": "string",
    "licenseExpiration": "string",
    "status": "string"
}
```

### 4. Actualizar Conductor
**Endpoint:** `/updateDriver`  
**Método:** PUT  
**Descripción:** Actualiza la información de un conductor existente.

**Headers:**
```
Content-Type: application/json
```

**Body:**
```json
{
    "uid": "string",
    "name": "string",
    "email": "string",
    "phone": "string",
    "licenseNumber": "string",
    "licenseExpiration": "string",
    "status": "string"
}
```

### 5. Crear Vehículo
**Endpoint:** `/createVehicle`  
**Método:** POST  
**Descripción:** Registra un nuevo vehículo en el sistema.

**Headers:**
```
Content-Type: application/json
```

**Body:**
```json
{
    "plate": "string",
    "brand": "string",
    "model": "string",
    "year": "number",
    "color": "string",
    "capacity": "number",
    "status": "string"
}
```

## Ejemplos de Uso en Postman

### Configuración Inicial
1. Abre Postman
2. Crea una nueva colección
3. Configura la variable de entorno `baseUrl` con el valor: `https://us-central1-balle-813e3.cloudfunctions.net`

### Ejemplo de Petición para Crear Usuario
1. Selecciona el método POST
2. URL: `{{baseUrl}}/createUser`
3. En la pestaña Headers, agrega:
   - Key: `Content-Type`
   - Value: `application/json`
4. En la pestaña Body, selecciona "raw" y "JSON", luego ingresa:
```json
{
    "email": "<EMAIL>",
    "password": "contraseña123",
    "displayName": "Usuario Ejemplo",
    "phoneNumber": "+1234567890",
    "role": "customer"
}
```

## Notas Importantes
- Los campos marcados como "string" deben ser texto
- Los campos marcados como "number" deben ser números
- Las fechas deben estar en formato ISO 8601
- Asegúrate de manejar adecuadamente los errores en las respuestas

## Códigos de Respuesta
- 200: Éxito
- 400: Error en la solicitud
- 401: No autorizado
- 403: Prohibido
- 404: No encontrado
- 500: Error del servidor 