<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo: Folio/Orden Colapsable</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1976d2, #42a5f5);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .info-box {
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 25px;
            border-left: 4px solid;
        }

        .info-box.success {
            background-color: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
        }

        .info-box.warning {
            background-color: #fff3e0;
            border-color: #ff9800;
            color: #f57c00;
        }

        .info-box.info {
            background-color: #e3f2fd;
            border-color: #2196f3;
            color: #1976d2;
        }

        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .demo-table th {
            background: #f5f5f5;
            padding: 15px 10px;
            text-align: center;
            font-weight: bold;
            color: #333;
            border-bottom: 2px solid #ddd;
            font-size: 13px;
        }

        .demo-table td {
            padding: 12px 10px;
            text-align: center;
            border-bottom: 1px solid #eee;
            font-size: 12px;
        }

        .demo-table tr:hover {
            background-color: #f8f9fa;
        }

        .folio-cell {
            cursor: pointer;
            min-width: 120px;
        }

        .folio-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 6px 10px;
            background-color: #f5f5f5;
            border-radius: 6px;
            border: 1px solid #ddd;
            transition: all 0.3s ease;
        }

        .folio-header:hover {
            background-color: #e3f2fd;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .folio-header.expanded {
            background-color: #e3f2fd;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .folio-text {
            font-weight: bold;
            font-size: 13px;
            color: #333;
        }

        .arrow {
            font-size: 12px;
            color: #666;
            transition: transform 0.3s ease;
            margin-left: 8px;
        }

        .arrow.expanded {
            transform: rotate(180deg);
        }

        .order-details {
            margin-top: 6px;
            padding: 8px 10px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 12px;
            color: #555;
            box-shadow: 0 3px 10px rgba(0,0,0,0.15);
            animation: slideDown 0.3s ease;
            display: none;
        }

        .order-details.show {
            display: block;
        }

        .order-number {
            margin-bottom: 4px;
            display: flex;
            align-items: center;
        }

        .order-badge {
            margin-left: 6px;
            background-color: #1976d2;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: bold;
        }

        .product-details {
            font-size: 11px;
            color: #777;
            margin-top: 4px;
            padding: 4px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #1976d2;
        }

        .status-badge {
            padding: 7px 15px;
            border-radius: 15px;
            font-weight: bold;
            color: white;
            font-size: 11px;
        }

        .status-complete {
            background-color: #4caf50;
        }

        .status-pending {
            background-color: #ff9800;
        }

        .status-cancelled {
            background-color: #f44336;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .instructions {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }

        .instructions h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }

        .instructions ul {
            margin: 0;
            padding-left: 20px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Folio/Orden Colapsable</h1>
            <p>Demostración de la nueva funcionalidad para la tabla de entregas</p>
        </div>

        <div class="content">
            <div class="info-box success">
                <h3>✅ Características Implementadas:</h3>
                <ul>
                    <li><strong>Columna Compacta:</strong> Muestra solo el folio inicialmente para ahorrar espacio</li>
                    <li><strong>Expansión al Clic:</strong> Al hacer clic se despliega la información de la orden</li>
                    <li><strong>Animación Suave:</strong> Transiciones fluidas para mejor experiencia de usuario</li>
                    <li><strong>Información Detallada:</strong> Muestra número de orden y detalles de productos</li>
                    <li><strong>Diseño Responsive:</strong> Se adapta a diferentes tamaños de pantalla</li>
                </ul>
            </div>

            <div class="info-box warning">
                <h3>💡 Beneficios:</h3>
                <ul>
                    <li><strong>Ahorro de Espacio:</strong> No necesitas columnas separadas para folio y orden</li>
                    <li><strong>Mejor UX:</strong> Los usuarios pueden ver detalles solo cuando los necesiten</li>
                    <li><strong>Información Contextual:</strong> Detalles de productos visibles bajo demanda</li>
                    <li><strong>Tabla Más Limpia:</strong> Menos columnas = mejor legibilidad</li>
                </ul>
            </div>

            <div class="instructions">
                <h4>Instrucciones:</h4>
                <ul>
                    <li>Haz clic en cualquier celda de la columna "Folio" para expandir/colapsar</li>
                    <li>Al expandir verás el número de orden y detalles adicionales</li>
                    <li>La animación suave mejora la experiencia del usuario</li>
                    <li>El diseño es responsive y se adapta al contenido</li>
                </ul>
            </div>

            <h2>Tabla de Ejemplo - Mis Entregas</h2>
            
            <table class="demo-table">
                <thead>
                    <tr>
                        <th>REFERENCIA DE ENTREGA</th>
                        <th>DOCUMENTO DE ENTREGA</th>
                        <th>FOLIO</th>
                        <th>NOMBRE DEL CLIENTE</th>
                        <th>FECHA DE ENTREGA</th>
                        <th>TIPO DE VEHÍCULO</th>
                        <th>ASIGNAR REPARTIDOR</th>
                        <th>ESTADO DE ENTREGA</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>GKBPPS</td>
                        <td>-</td>
                        <td class="folio-cell">
                            <div class="folio-header" onclick="toggleOrder(this)">
                                <span class="folio-text">ENT-2024-001</span>
                                <span class="arrow">▼</span>
                            </div>
                            <div class="order-details">
                                <div class="order-number">
                                    <strong style="color: #1976d2;">Orden:</strong>
                                    <span class="order-badge">#1</span>
                                </div>
                                <div class="product-details">
                                    Productos: 3x Laptop, 2x Mouse
                                </div>
                            </div>
                        </td>
                        <td>Jonathan Ballesteros</td>
                        <td>20 de jul. de 2025 9:45</td>
                        <td>CAMIONETA</td>
                        <td>Diego Ballesteros</td>
                        <td><span class="status-badge status-complete">COMPLETA</span></td>
                    </tr>
                    <tr>
                        <td>WADKNU</td>
                        <td>OCG-968598</td>
                        <td class="folio-cell">
                            <div class="folio-header" onclick="toggleOrder(this)">
                                <span class="folio-text">ENT-2024-002</span>
                                <span class="arrow">▼</span>
                            </div>
                            <div class="order-details">
                                <div class="order-number">
                                    <strong style="color: #1976d2;">Orden:</strong>
                                    <span class="order-badge">#2</span>
                                </div>
                                <div class="product-details">
                                    Productos: 1x Impresora, 5x Cartuchos
                                </div>
                            </div>
                        </td>
                        <td>Jonathan Ballesteros</td>
                        <td>4 de abr. de 2025 13:24</td>
                        <td>MOTOCICLETA</td>
                        <td>Diego Ballesteros</td>
                        <td><span class="status-badge status-complete">COMPLETA</span></td>
                    </tr>
                    <tr>
                        <td>GDY496Y3</td>
                        <td>-</td>
                        <td class="folio-cell">
                            <div class="folio-header" onclick="toggleOrder(this)">
                                <span class="folio-text">ENT-2024-001</span>
                                <span class="arrow">▼</span>
                            </div>
                            <div class="order-details">
                                <div class="order-number">
                                    <strong style="color: #1976d2;">Orden:</strong>
                                    <span class="order-badge">#3</span>
                                </div>
                                <div class="product-details">
                                    Productos: 2x Monitor, 1x Teclado
                                </div>
                            </div>
                        </td>
                        <td>Juan Pérez Test</td>
                        <td>23 de jul. de 2025 10:47</td>
                        <td>CAMIONETA</td>
                        <td>Diego Ballesteros</td>
                        <td><span class="status-badge status-complete">COMPLETA</span></td>
                    </tr>
                    <tr>
                        <td>GDNRBL1N</td>
                        <td>-</td>
                        <td class="folio-cell">
                            <div class="folio-header" onclick="toggleOrder(this)">
                                <span class="folio-text">ENT-2024-003</span>
                                <span class="arrow">▼</span>
                            </div>
                            <div class="order-details">
                                <div class="order-number">
                                    <strong style="color: #1976d2;">Orden:</strong>
                                    <span class="order-badge">#1</span>
                                </div>
                                <div class="product-details">
                                    Productos: 1x Tablet, 1x Funda
                                </div>
                            </div>
                        </td>
                        <td>María García Test</td>
                        <td>23 de jul. de 2025 10:47</td>
                        <td>CAMIONETA</td>
                        <td>Diego Ballesteros</td>
                        <td><span class="status-badge status-complete">COMPLETA</span></td>
                    </tr>
                </tbody>
            </table>

            <div class="info-box info">
                <h3>🔧 Implementación Técnica:</h3>
                <ul>
                    <li><strong>React Hooks:</strong> useState para manejar el estado de expansión</li>
                    <li><strong>CSS Transitions:</strong> Animaciones suaves con transform y opacity</li>
                    <li><strong>Componente Reutilizable:</strong> FolioOrderCell puede usarse en otras tablas</li>
                    <li><strong>Responsive Design:</strong> Adapta el ancho según el contenido</li>
                    <li><strong>Accesibilidad:</strong> Cursor pointer y indicadores visuales claros</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function toggleOrder(element) {
            const arrow = element.querySelector('.arrow');
            const orderDetails = element.nextElementSibling;
            
            // Toggle expanded class
            element.classList.toggle('expanded');
            arrow.classList.toggle('expanded');
            
            // Toggle order details visibility
            if (orderDetails.classList.contains('show')) {
                orderDetails.classList.remove('show');
            } else {
                orderDetails.classList.add('show');
            }
        }
    </script>
</body>
</html>
