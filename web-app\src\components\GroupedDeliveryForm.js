/**
 * Componente para crear entregas agrupadas usando los nuevos servicios API
 */

import React, { useState, useEffect } from 'react';
import {
    Card,
    CardContent,
    TextField,
    Button,
    Grid,
    Typography,
    Alert,
    CircularProgress,
    Box,
    IconButton,
    Divider,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    Chip
} from '@mui/material';
import {
    Add as AddIcon,
    Delete as DeleteIcon,
    ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import { useDeliveryService, useNotification } from '../hooks/useApiService';

const GroupedDeliveryForm = ({ onSuccess, onCancel }) => {
    const [formData, setFormData] = useState({
        folio: '',
        driver: '',
        vehicle: {
            type: '',
            image: '',
            plate: ''
        },
        orders: [],
        estimatedRoute: [],
        totalDistance: 0,
        totalEstimatedTime: 0
    });

    // Hooks para servicios API
    const { createGroupedDelivery } = useDeliveryService();
    const { notification, showSuccess, showError, clearNotification } = useNotification();

    useEffect(() => {
        // Generar folio automático
        const generateFolio = () => {
            const date = new Date();
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            return `ENT-${year}${month}${day}-${random}`;
        };

        setFormData(prev => ({
            ...prev,
            folio: generateFolio()
        }));
    }, []);

    const handleInputChange = (field) => (event) => {
        setFormData(prev => ({
            ...prev,
            [field]: event.target.value
        }));
    };

    const handleVehicleChange = (field) => (event) => {
        setFormData(prev => ({
            ...prev,
            vehicle: {
                ...prev.vehicle,
                [field]: event.target.value
            }
        }));
    };

    const addNewOrder = () => {
        const newOrder = {
            customerId: '',
            customerName: '',
            customerEmail: '',
            customerPhone: '',
            customerToken: '',
            pickupAddress: {
                lat: 0,
                lng: 0,
                address: ''
            },
            deliveryAddress: {
                lat: 0,
                lng: 0,
                address: ''
            },
            estimatedFare: 0,
            estimatedDistance: 0,
            estimatedTime: 0,
            notes: '',
            products: []
        };

        setFormData(prev => ({
            ...prev,
            orders: [...prev.orders, newOrder]
        }));
    };

    const removeOrder = (index) => {
        setFormData(prev => ({
            ...prev,
            orders: prev.orders.filter((_, i) => i !== index)
        }));
    };

    const updateOrder = (index, field, value) => {
        setFormData(prev => ({
            ...prev,
            orders: prev.orders.map((order, i) => 
                i === index ? { ...order, [field]: value } : order
            )
        }));
    };

    const updateOrderAddress = (orderIndex, addressType, field, value) => {
        setFormData(prev => ({
            ...prev,
            orders: prev.orders.map((order, i) => 
                i === orderIndex ? {
                    ...order,
                    [addressType]: {
                        ...order[addressType],
                        [field]: value
                    }
                } : order
            )
        }));
    };

    const addProductToOrder = (orderIndex) => {
        const newProduct = {
            id: '',
            name: '',
            quantity: 1,
            sku: ''
        };

        setFormData(prev => ({
            ...prev,
            orders: prev.orders.map((order, i) => 
                i === orderIndex ? {
                    ...order,
                    products: [...order.products, newProduct]
                } : order
            )
        }));
    };

    const updateProduct = (orderIndex, productIndex, field, value) => {
        setFormData(prev => ({
            ...prev,
            orders: prev.orders.map((order, i) => 
                i === orderIndex ? {
                    ...order,
                    products: order.products.map((product, j) => 
                        j === productIndex ? { ...product, [field]: value } : product
                    )
                } : order
            )
        }));
    };

    const removeProduct = (orderIndex, productIndex) => {
        setFormData(prev => ({
            ...prev,
            orders: prev.orders.map((order, i) => 
                i === orderIndex ? {
                    ...order,
                    products: order.products.filter((_, j) => j !== productIndex)
                } : order
            )
        }));
    };

    const validateForm = () => {
        if (!formData.folio.trim()) {
            showError('El folio es requerido');
            return false;
        }

        if (!formData.driver.trim()) {
            showError('El conductor es requerido');
            return false;
        }

        if (!formData.vehicle.type.trim() || !formData.vehicle.plate.trim()) {
            showError('Los datos del vehículo son requeridos');
            return false;
        }

        if (formData.orders.length === 0) {
            showError('Debe agregar al menos una orden');
            return false;
        }

        // Validar cada orden
        for (let i = 0; i < formData.orders.length; i++) {
            const order = formData.orders[i];
            if (!order.customerId || !order.customerName || !order.customerEmail || 
                !order.pickupAddress.address || !order.deliveryAddress.address) {
                showError(`La orden ${i + 1} tiene campos faltantes`);
                return false;
            }

            if (order.products.length === 0) {
                showError(`La orden ${i + 1} debe tener al menos un producto`);
                return false;
            }
        }

        return true;
    };

    const handleSubmit = async (event) => {
        event.preventDefault();
        clearNotification();

        if (!validateForm()) {
            return;
        }

        try {
            const result = await createGroupedDelivery.execute(formData);
            showSuccess('Entrega agrupada creada exitosamente');

            if (onSuccess) {
                onSuccess(result);
            }
        } catch (error) {
            showError(error.message || 'Error al crear la entrega agrupada');
        }
    };

    const isLoading = createGroupedDelivery.loading;

    return (
        <Card>
            <CardContent>
                <Typography variant="h5" component="h2" gutterBottom>
                    Crear Entrega Agrupada
                </Typography>

                {notification && (
                    <Alert 
                        severity={notification.type} 
                        onClose={clearNotification}
                        sx={{ mb: 2 }}
                    >
                        {notification.message}
                    </Alert>
                )}

                <form onSubmit={handleSubmit}>
                    <Grid container spacing={2}>
                        {/* Información básica */}
                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="Folio"
                                value={formData.folio}
                                onChange={handleInputChange('folio')}
                                required
                                disabled={isLoading}
                            />
                        </Grid>
                        
                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="ID del Conductor"
                                value={formData.driver}
                                onChange={handleInputChange('driver')}
                                required
                                disabled={isLoading}
                            />
                        </Grid>

                        {/* Información del vehículo */}
                        <Grid item xs={12}>
                            <Typography variant="h6" gutterBottom>
                                Información del Vehículo
                            </Typography>
                        </Grid>

                        <Grid item xs={12} sm={4}>
                            <TextField
                                fullWidth
                                label="Tipo de Vehículo"
                                value={formData.vehicle.type}
                                onChange={handleVehicleChange('type')}
                                required
                                disabled={isLoading}
                            />
                        </Grid>

                        <Grid item xs={12} sm={4}>
                            <TextField
                                fullWidth
                                label="Placa"
                                value={formData.vehicle.plate}
                                onChange={handleVehicleChange('plate')}
                                required
                                disabled={isLoading}
                            />
                        </Grid>

                        <Grid item xs={12} sm={4}>
                            <TextField
                                fullWidth
                                label="URL de Imagen"
                                value={formData.vehicle.image}
                                onChange={handleVehicleChange('image')}
                                disabled={isLoading}
                            />
                        </Grid>

                        {/* Órdenes */}
                        <Grid item xs={12}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                                <Typography variant="h6">
                                    Órdenes ({formData.orders.length})
                                </Typography>
                                <Button
                                    variant="outlined"
                                    startIcon={<AddIcon />}
                                    onClick={addNewOrder}
                                    disabled={isLoading}
                                >
                                    Agregar Orden
                                </Button>
                            </Box>

                            {formData.orders.map((order, orderIndex) => (
                                <Accordion key={orderIndex} sx={{ mb: 1 }}>
                                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                            <Typography>
                                                Orden {orderIndex + 1}: {order.customerName || 'Sin nombre'}
                                            </Typography>
                                            <Chip 
                                                label={`${order.products.length} productos`} 
                                                size="small" 
                                                color="primary" 
                                            />
                                            <IconButton
                                                size="small"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    removeOrder(orderIndex);
                                                }}
                                                disabled={isLoading}
                                            >
                                                <DeleteIcon />
                                            </IconButton>
                                        </Box>
                                    </AccordionSummary>
                                    <AccordionDetails>
                                        <Grid container spacing={2}>
                                            {/* Información del cliente */}
                                            <Grid item xs={12} sm={6}>
                                                <TextField
                                                    fullWidth
                                                    label="ID del Cliente"
                                                    value={order.customerId}
                                                    onChange={(e) => updateOrder(orderIndex, 'customerId', e.target.value)}
                                                    required
                                                    disabled={isLoading}
                                                />
                                            </Grid>
                                            <Grid item xs={12} sm={6}>
                                                <TextField
                                                    fullWidth
                                                    label="Nombre del Cliente"
                                                    value={order.customerName}
                                                    onChange={(e) => updateOrder(orderIndex, 'customerName', e.target.value)}
                                                    required
                                                    disabled={isLoading}
                                                />
                                            </Grid>
                                            <Grid item xs={12} sm={6}>
                                                <TextField
                                                    fullWidth
                                                    label="Email del Cliente"
                                                    type="email"
                                                    value={order.customerEmail}
                                                    onChange={(e) => updateOrder(orderIndex, 'customerEmail', e.target.value)}
                                                    required
                                                    disabled={isLoading}
                                                />
                                            </Grid>
                                            <Grid item xs={12} sm={6}>
                                                <TextField
                                                    fullWidth
                                                    label="Teléfono del Cliente"
                                                    value={order.customerPhone}
                                                    onChange={(e) => updateOrder(orderIndex, 'customerPhone', e.target.value)}
                                                    disabled={isLoading}
                                                />
                                            </Grid>

                                            {/* Direcciones */}
                                            <Grid item xs={12}>
                                                <Typography variant="subtitle2" gutterBottom>
                                                    Dirección de Recogida
                                                </Typography>
                                            </Grid>
                                            <Grid item xs={12}>
                                                <TextField
                                                    fullWidth
                                                    label="Dirección de Recogida"
                                                    value={order.pickupAddress.address}
                                                    onChange={(e) => updateOrderAddress(orderIndex, 'pickupAddress', 'address', e.target.value)}
                                                    required
                                                    disabled={isLoading}
                                                />
                                            </Grid>

                                            <Grid item xs={12}>
                                                <Typography variant="subtitle2" gutterBottom>
                                                    Dirección de Entrega
                                                </Typography>
                                            </Grid>
                                            <Grid item xs={12}>
                                                <TextField
                                                    fullWidth
                                                    label="Dirección de Entrega"
                                                    value={order.deliveryAddress.address}
                                                    onChange={(e) => updateOrderAddress(orderIndex, 'deliveryAddress', 'address', e.target.value)}
                                                    required
                                                    disabled={isLoading}
                                                />
                                            </Grid>

                                            {/* Productos */}
                                            <Grid item xs={12}>
                                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2, mb: 1 }}>
                                                    <Typography variant="subtitle2">
                                                        Productos ({order.products.length})
                                                    </Typography>
                                                    <Button
                                                        size="small"
                                                        variant="outlined"
                                                        startIcon={<AddIcon />}
                                                        onClick={() => addProductToOrder(orderIndex)}
                                                        disabled={isLoading}
                                                    >
                                                        Agregar Producto
                                                    </Button>
                                                </Box>

                                                {order.products.map((product, productIndex) => (
                                                    <Box key={productIndex} sx={{ border: 1, borderColor: 'grey.300', p: 2, mb: 1, borderRadius: 1 }}>
                                                        <Grid container spacing={2}>
                                                            <Grid item xs={12} sm={4}>
                                                                <TextField
                                                                    fullWidth
                                                                    label="Nombre del Producto"
                                                                    value={product.name}
                                                                    onChange={(e) => updateProduct(orderIndex, productIndex, 'name', e.target.value)}
                                                                    required
                                                                    disabled={isLoading}
                                                                />
                                                            </Grid>
                                                            <Grid item xs={12} sm={3}>
                                                                <TextField
                                                                    fullWidth
                                                                    label="Cantidad"
                                                                    type="number"
                                                                    value={product.quantity}
                                                                    onChange={(e) => updateProduct(orderIndex, productIndex, 'quantity', parseInt(e.target.value) || 1)}
                                                                    required
                                                                    disabled={isLoading}
                                                                />
                                                            </Grid>
                                                            <Grid item xs={12} sm={4}>
                                                                <TextField
                                                                    fullWidth
                                                                    label="SKU"
                                                                    value={product.sku}
                                                                    onChange={(e) => updateProduct(orderIndex, productIndex, 'sku', e.target.value)}
                                                                    disabled={isLoading}
                                                                />
                                                            </Grid>
                                                            <Grid item xs={12} sm={1}>
                                                                <IconButton
                                                                    onClick={() => removeProduct(orderIndex, productIndex)}
                                                                    disabled={isLoading}
                                                                    color="error"
                                                                >
                                                                    <DeleteIcon />
                                                                </IconButton>
                                                            </Grid>
                                                        </Grid>
                                                    </Box>
                                                ))}
                                            </Grid>
                                        </Grid>
                                    </AccordionDetails>
                                </Accordion>
                            ))}
                        </Grid>

                        <Grid item xs={12}>
                            <Divider sx={{ my: 2 }} />
                            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                                {onCancel && (
                                    <Button
                                        variant="outlined"
                                        onClick={onCancel}
                                        disabled={isLoading}
                                    >
                                        Cancelar
                                    </Button>
                                )}
                                
                                <Button
                                    type="submit"
                                    variant="contained"
                                    disabled={isLoading || formData.orders.length === 0}
                                    startIcon={isLoading && <CircularProgress size={20} />}
                                >
                                    {isLoading ? 'Creando...' : 'Crear Entrega Agrupada'}
                                </Button>
                            </Box>
                        </Grid>
                    </Grid>
                </form>
            </CardContent>
        </Card>
    );
};

export default GroupedDeliveryForm;
