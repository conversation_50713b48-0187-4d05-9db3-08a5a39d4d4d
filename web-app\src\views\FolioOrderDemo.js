import React from 'react';
import FolioOrderExample from '../components/FolioOrderExample';
import { Container, Paper, Typography, Box } from '@mui/material';
import { colors } from '../components/Theme/WebTheme';

const FolioOrderDemo = () => {
  return (
    <Container maxWidth="xl" style={{ padding: '20px' }}>
      <Paper 
        elevation={3} 
        style={{ 
          padding: '30px', 
          backgroundColor: '#fff',
          borderRadius: '12px'
        }}
      >
        <Box style={{ marginBottom: '30px', textAlign: 'center' }}>
          <Typography 
            variant="h4" 
            style={{ 
              color: colors.BLACK,
              fontWeight: 'bold',
              marginBottom: '10px',
              fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif'
            }}
          >
            🚀 Nueva Funcionalidad: Folio/Orden Colapsable
          </Typography>
          
          <Typography 
            variant="h6" 
            style={{ 
              color: '#666',
              marginBottom: '20px',
              fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif'
            }}
          >
            Demostración de la columna interactiva que combina Folio y Orden
          </Typography>
        </Box>

        <Box style={{
          backgroundColor: '#e8f5e8',
          padding: '20px',
          borderRadius: '8px',
          marginBottom: '30px',
          border: '1px solid #c8e6c9'
        }}>
          <Typography variant="h6" style={{ 
            color: '#2e7d32', 
            marginBottom: '15px',
            fontWeight: 'bold'
          }}>
            ✅ Características Implementadas:
          </Typography>
          
          <ul style={{ 
            color: '#2e7d32', 
            lineHeight: '1.8',
            paddingLeft: '20px'
          }}>
            <li><strong>Columna Compacta:</strong> Muestra solo el folio inicialmente para ahorrar espacio</li>
            <li><strong>Expansión al Clic:</strong> Al hacer clic se despliega la información de la orden</li>
            <li><strong>Animación Suave:</strong> Transiciones fluidas para mejor experiencia de usuario</li>
            <li><strong>Información Detallada:</strong> Muestra número de orden y detalles de productos</li>
            <li><strong>Diseño Responsive:</strong> Se adapta a diferentes tamaños de pantalla</li>
            <li><strong>Indicador Visual:</strong> Flecha que rota para mostrar el estado (expandido/colapsado)</li>
          </ul>
        </Box>

        <Box style={{
          backgroundColor: '#fff3e0',
          padding: '20px',
          borderRadius: '8px',
          marginBottom: '30px',
          border: '1px solid #ffcc02'
        }}>
          <Typography variant="h6" style={{ 
            color: '#f57c00', 
            marginBottom: '15px',
            fontWeight: 'bold'
          }}>
            💡 Beneficios de esta Implementación:
          </Typography>
          
          <ul style={{ 
            color: '#f57c00', 
            lineHeight: '1.8',
            paddingLeft: '20px'
          }}>
            <li><strong>Ahorro de Espacio:</strong> No necesitas columnas separadas para folio y orden</li>
            <li><strong>Mejor UX:</strong> Los usuarios pueden ver detalles solo cuando los necesiten</li>
            <li><strong>Información Contextual:</strong> Detalles de productos visibles bajo demanda</li>
            <li><strong>Tabla Más Limpia:</strong> Menos columnas = mejor legibilidad</li>
            <li><strong>Escalable:</strong> Fácil agregar más información en el área expandida</li>
          </ul>
        </Box>

        <Box style={{
          backgroundColor: '#e3f2fd',
          padding: '20px',
          borderRadius: '8px',
          marginBottom: '30px',
          border: '1px solid #2196f3'
        }}>
          <Typography variant="h6" style={{ 
            color: '#1976d2', 
            marginBottom: '15px',
            fontWeight: 'bold'
          }}>
            🔧 Implementación Técnica:
          </Typography>
          
          <ul style={{ 
            color: '#1976d2', 
            lineHeight: '1.8',
            paddingLeft: '20px'
          }}>
            <li><strong>React Hooks:</strong> useState para manejar el estado de expansión</li>
            <li><strong>CSS Transitions:</strong> Animaciones suaves con transform y opacity</li>
            <li><strong>Componente Reutilizable:</strong> FolioOrderCell puede usarse en otras tablas</li>
            <li><strong>Responsive Design:</strong> Adapta el ancho según el contenido</li>
            <li><strong>Accesibilidad:</strong> Cursor pointer y indicadores visuales claros</li>
          </ul>
        </Box>

        <FolioOrderExample />
        
        <Box style={{
          backgroundColor: '#f5f5f5',
          padding: '20px',
          borderRadius: '8px',
          marginTop: '30px',
          textAlign: 'center'
        }}>
          <Typography variant="body1" style={{ 
            color: '#666',
            fontStyle: 'italic'
          }}>
            💻 Esta funcionalidad está lista para ser integrada en la tabla principal de BookingHistory.js
          </Typography>
        </Box>
      </Paper>
    </Container>
  );
};

export default FolioOrderDemo;
