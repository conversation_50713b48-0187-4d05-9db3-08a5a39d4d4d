/**
 * Hooks personalizados para usar los servicios API
 * Facilita el manejo de estado y loading en los componentes React
 */

import { useState, useCallback } from 'react';
import apiService, { handleApiError } from '../services/apiService';

/**
 * Hook para manejar operaciones de API con estado de loading y error
 * @param {Function} apiFunction - Función de API a ejecutar
 * @returns {object} - Estado y funciones para manejar la API
 */
export const useApiCall = (apiFunction) => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [data, setData] = useState(null);

    const execute = useCallback(async (...args) => {
        try {
            setLoading(true);
            setError(null);
            const result = await apiFunction(...args);
            setData(result);
            return result;
        } catch (err) {
            const errorResult = handleApiError(err, apiFunction.name);
            setError(errorResult);
            throw err;
        } finally {
            setLoading(false);
        }
    }, [apiFunction]);

    const reset = useCallback(() => {
        setLoading(false);
        setError(null);
        setData(null);
    }, []);

    return {
        loading,
        error,
        data,
        execute,
        reset
    };
};

/**
 * Hook específico para operaciones de clientes
 */
export const useCustomerService = () => {
    const createCustomer = useApiCall(apiService.customer.create);
    const updateCustomer = useApiCall(apiService.customer.update);

    return {
        createCustomer,
        updateCustomer
    };
};

/**
 * Hook específico para operaciones de conductores
 */
export const useDriverService = () => {
    const createDriver = useApiCall(apiService.driver.create);
    const updateDriver = useApiCall(apiService.driver.update);

    return {
        createDriver,
        updateDriver
    };
};

/**
 * Hook específico para operaciones de vehículos
 */
export const useVehicleService = () => {
    const createVehicle = useApiCall(apiService.vehicle.create);
    const updateVehicle = useApiCall(apiService.vehicle.update);

    return {
        createVehicle,
        updateVehicle
    };
};

/**
 * Hook específico para operaciones de entregas
 */
export const useDeliveryService = () => {
    const createGroupedDelivery = useApiCall(apiService.delivery.createGroupedDelivery);

    return {
        createGroupedDelivery
    };
};

/**
 * Hook combinado que incluye todos los servicios
 */
export const useAllServices = () => {
    const customerService = useCustomerService();
    const driverService = useDriverService();
    const vehicleService = useVehicleService();
    const deliveryService = useDeliveryService();

    return {
        customer: customerService,
        driver: driverService,
        vehicle: vehicleService,
        delivery: deliveryService
    };
};

/**
 * Hook para manejar formularios con validación básica
 * @param {object} initialValues - Valores iniciales del formulario
 * @param {Function} validationSchema - Función de validación
 * @returns {object} - Estado y funciones del formulario
 */
export const useFormWithValidation = (initialValues, validationSchema) => {
    const [values, setValues] = useState(initialValues);
    const [errors, setErrors] = useState({});
    const [touched, setTouched] = useState({});

    const handleChange = useCallback((name, value) => {
        setValues(prev => ({
            ...prev,
            [name]: value
        }));

        // Limpiar error cuando el usuario empiece a escribir
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: null
            }));
        }
    }, [errors]);

    const handleBlur = useCallback((name) => {
        setTouched(prev => ({
            ...prev,
            [name]: true
        }));

        // Validar campo individual
        if (validationSchema) {
            try {
                validationSchema.validateSyncAt(name, values);
                setErrors(prev => ({
                    ...prev,
                    [name]: null
                }));
            } catch (error) {
                setErrors(prev => ({
                    ...prev,
                    [name]: error.message
                }));
            }
        }
    }, [values, validationSchema]);

    const validate = useCallback(() => {
        if (!validationSchema) return true;

        try {
            validationSchema.validateSync(values, { abortEarly: false });
            setErrors({});
            return true;
        } catch (error) {
            const newErrors = {};
            error.inner.forEach(err => {
                newErrors[err.path] = err.message;
            });
            setErrors(newErrors);
            return false;
        }
    }, [values, validationSchema]);

    const reset = useCallback(() => {
        setValues(initialValues);
        setErrors({});
        setTouched({});
    }, [initialValues]);

    return {
        values,
        errors,
        touched,
        handleChange,
        handleBlur,
        validate,
        reset,
        isValid: Object.keys(errors).length === 0
    };
};

/**
 * Hook para manejar notificaciones/alertas
 */
export const useNotification = () => {
    const [notification, setNotification] = useState(null);

    const showSuccess = useCallback((message) => {
        setNotification({
            type: 'success',
            message,
            timestamp: Date.now()
        });
    }, []);

    const showError = useCallback((message) => {
        setNotification({
            type: 'error',
            message,
            timestamp: Date.now()
        });
    }, []);

    const showWarning = useCallback((message) => {
        setNotification({
            type: 'warning',
            message,
            timestamp: Date.now()
        });
    }, []);

    const showInfo = useCallback((message) => {
        setNotification({
            type: 'info',
            message,
            timestamp: Date.now()
        });
    }, []);

    const clearNotification = useCallback(() => {
        setNotification(null);
    }, []);

    return {
        notification,
        showSuccess,
        showError,
        showWarning,
        showInfo,
        clearNotification
    };
};

export default {
    useApiCall,
    useCustomerService,
    useDriverService,
    useVehicleService,
    useDeliveryService,
    useAllServices,
    useFormWithValidation,
    useNotification
};
