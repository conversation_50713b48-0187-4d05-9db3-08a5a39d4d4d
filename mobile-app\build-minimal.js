const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Backup original config
const originalConfig = './app.config.js';
const basicConfig = './app.config.basic.js';
const backupConfig = './app.config.backup.js';

console.log('🔄 Preparando build con configuración mínima...');

try {
    // Backup original config
    if (fs.existsSync(originalConfig)) {
        fs.copyFileSync(originalConfig, backupConfig);
        console.log('✅ Backup de configuración original creado');
    }

    // Copy basic config to main config
    fs.copyFileSync(basicConfig, originalConfig);
    console.log('✅ Configuración básica aplicada');

    // Run EAS build
    console.log('🚀 Iniciando build con configuración mínima...');
    execSync('eas build --platform android --profile minimal --clear-cache', { 
        stdio: 'inherit',
        cwd: process.cwd()
    });

} catch (error) {
    console.error('❌ Error durante el build:', error.message);
} finally {
    // Restore original config
    if (fs.existsSync(backupConfig)) {
        fs.copyFileSync(backupConfig, originalConfig);
        fs.unlinkSync(backupConfig);
        console.log('✅ Configuración original restaurada');
    }
} 