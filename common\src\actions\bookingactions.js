import {
    CONFIRM_BOOKING,
    CONFIRM_BOOKING_SUCCESS,
    CONFIRM_BOOKING_FAILED,
    CLEAR_BOOKING
} from "../store/types";
import { RequestPushMsg } from '../other/NotificationFunctions';
import store from '../store/store';
import { firebase } from '../config/configureFirebase';
import { formatBookingObject } from '../other/sharedFunctions';
import { get, onValue, push } from "firebase/database";

export const clearBooking = () => (dispatch) => {
    dispatch({
        type: CLEAR_BOOKING,
        payload: null,
    });
}

export const addBooking = (bookingData) => async (dispatch) => {

    const   {
        bookingRef,
        settingsRef,
        singleUserRef
    } = firebase;

    dispatch({
        type: CONFIRM_BOOKING,
        payload: bookingData,
    });

    try {
        const settingsdata = await get(settingsRef);
        const settings = settingsdata.val();

        if (!settings) {
            throw new Error("No se pudieron obtener las configuraciones del sistema");
        }

        let data = await formatBookingObject(bookingData, settings);

        if(bookingData.requestedDrivers){
            const drivers = bookingData.requestedDrivers;
            Object.keys(drivers).map((uid)=>{
                onValue(singleUserRef(uid),  snapshot => {
                    if (snapshot.val()) {
                        const pushToken = snapshot.val().pushToken;
                        const ios = snapshot.val().userPlatform == "IOS"? true: false
                        if(pushToken){
                            RequestPushMsg(
                                pushToken,
                                {
                                    title: store.getState().languagedata.defaultLanguage.notification_title,
                                    msg: store.getState().languagedata.defaultLanguage.new_booking_notification,
                                    screen: 'DriverTrips',
                                    channelId: settings.CarHornRepeat? 'bookings-repeat': 'bookings',
                                    ios: ios
                                });
                         }
                     }
                }, {onlyOnce: true});
                return drivers[uid];
            })
        }

        const res = await push(bookingRef, data);
        var bookingKey = res.key;
        
        dispatch({
            type: CONFIRM_BOOKING_SUCCESS,
            payload: {
                booking_id:bookingKey,
                mainData:{
                    ...data,
                    id:bookingKey
                }
            }    
        });
    } catch (error) {
        console.error("Error al crear booking:", error);
        let errorMessage = "Error desconocido al crear el booking";
        
        if (error.code) {
            switch (error.code) {
                case 'PERMISSION_DENIED':
                    errorMessage = "No tienes permisos para crear bookings. Verifica tu tipo de usuario.";
                    break;
                case 'UNAUTHENTICATED':
                    errorMessage = "No estás autenticado. Inicia sesión nuevamente.";
                    break;
                case 'INVALID_ARGUMENT':
                    errorMessage = "Datos de booking inválidos. Verifica la información proporcionada.";
                    break;
                default:
                    errorMessage = `Error ${error.code}: ${error.message}`;
            }
        } else if (error.message) {
            errorMessage = error.message;
        }
        
        dispatch({
            type: CONFIRM_BOOKING_FAILED,
            payload: errorMessage,
        });
    }
};

