/**
 * Hooks personalizados para React Native para usar los servicios API
 * Facilita el manejo de estado y loading en los componentes móviles
 */

import { useState, useCallback } from 'react';
import {
    createCustomer,
    updateCustomer,
    createDriver,
    updateDriver,
    createVehicle,
    updateVehicle,
    createGroupedDelivery
} from '../common/sharedFunctions';

/**
 * Hook para manejar operaciones de API con estado de loading y error
 * @param {Function} apiFunction - Función de API a ejecutar
 * @returns {object} - Estado y funciones para manejar la API
 */
export const useApiCall = (apiFunction) => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [data, setData] = useState(null);

    const execute = useCallback(async (...args) => {
        try {
            setLoading(true);
            setError(null);
            const result = await apiFunction(...args);
            setData(result);
            return result;
        } catch (err) {
            const errorMessage = err.message || 'Error desconocido';
            setError(errorMessage);
            throw err;
        } finally {
            setLoading(false);
        }
    }, [apiFunction]);

    const reset = useCallback(() => {
        setLoading(false);
        setError(null);
        setData(null);
    }, []);

    return {
        loading,
        error,
        data,
        execute,
        reset
    };
};

/**
 * Hook específico para operaciones de clientes
 */
export const useCustomerService = () => {
    const createCustomerCall = useApiCall(createCustomer);
    const updateCustomerCall = useApiCall(updateCustomer);

    return {
        createCustomer: createCustomerCall,
        updateCustomer: updateCustomerCall
    };
};

/**
 * Hook específico para operaciones de conductores
 */
export const useDriverService = () => {
    const createDriverCall = useApiCall(createDriver);
    const updateDriverCall = useApiCall(updateDriver);

    return {
        createDriver: createDriverCall,
        updateDriver: updateDriverCall
    };
};

/**
 * Hook específico para operaciones de vehículos
 */
export const useVehicleService = () => {
    const createVehicleCall = useApiCall(createVehicle);
    const updateVehicleCall = useApiCall(updateVehicle);

    return {
        createVehicle: createVehicleCall,
        updateVehicle: updateVehicleCall
    };
};

/**
 * Hook específico para operaciones de entregas
 */
export const useDeliveryService = () => {
    const createGroupedDeliveryCall = useApiCall(createGroupedDelivery);

    return {
        createGroupedDelivery: createGroupedDeliveryCall
    };
};

/**
 * Hook combinado que incluye todos los servicios
 */
export const useAllServices = () => {
    const customerService = useCustomerService();
    const driverService = useDriverService();
    const vehicleService = useVehicleService();
    const deliveryService = useDeliveryService();

    return {
        customer: customerService,
        driver: driverService,
        vehicle: vehicleService,
        delivery: deliveryService
    };
};

/**
 * Hook para manejar formularios con validación básica para React Native
 * @param {object} initialValues - Valores iniciales del formulario
 * @param {Function} validationFunction - Función de validación personalizada
 * @returns {object} - Estado y funciones del formulario
 */
export const useFormWithValidation = (initialValues, validationFunction) => {
    const [values, setValues] = useState(initialValues);
    const [errors, setErrors] = useState({});
    const [touched, setTouched] = useState({});

    const handleChange = useCallback((name, value) => {
        setValues(prev => ({
            ...prev,
            [name]: value
        }));

        // Limpiar error cuando el usuario empiece a escribir
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: null
            }));
        }
    }, [errors]);

    const handleBlur = useCallback((name) => {
        setTouched(prev => ({
            ...prev,
            [name]: true
        }));

        // Validar campo individual si hay función de validación
        if (validationFunction) {
            const fieldErrors = validationFunction(values);
            if (fieldErrors[name]) {
                setErrors(prev => ({
                    ...prev,
                    [name]: fieldErrors[name]
                }));
            }
        }
    }, [values, validationFunction]);

    const validate = useCallback(() => {
        if (!validationFunction) return true;

        const newErrors = validationFunction(values);
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    }, [values, validationFunction]);

    const reset = useCallback(() => {
        setValues(initialValues);
        setErrors({});
        setTouched({});
    }, [initialValues]);

    return {
        values,
        errors,
        touched,
        handleChange,
        handleBlur,
        validate,
        reset,
        isValid: Object.keys(errors).length === 0
    };
};

/**
 * Hook para manejar alertas/notificaciones en React Native
 */
export const useAlert = () => {
    const [alert, setAlert] = useState(null);

    const showSuccess = useCallback((message, title = 'Éxito') => {
        setAlert({
            type: 'success',
            title,
            message,
            timestamp: Date.now()
        });
    }, []);

    const showError = useCallback((message, title = 'Error') => {
        setAlert({
            type: 'error',
            title,
            message,
            timestamp: Date.now()
        });
    }, []);

    const showWarning = useCallback((message, title = 'Advertencia') => {
        setAlert({
            type: 'warning',
            title,
            message,
            timestamp: Date.now()
        });
    }, []);

    const showInfo = useCallback((message, title = 'Información') => {
        setAlert({
            type: 'info',
            title,
            message,
            timestamp: Date.now()
        });
    }, []);

    const clearAlert = useCallback(() => {
        setAlert(null);
    }, []);

    return {
        alert,
        showSuccess,
        showError,
        showWarning,
        showInfo,
        clearAlert
    };
};

/**
 * Funciones de validación comunes para formularios
 */
export const validationHelpers = {
    /**
     * Validar email
     */
    validateEmail: (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    /**
     * Validar teléfono móvil (debe empezar con +)
     */
    validateMobile: (mobile) => {
        return mobile && mobile.startsWith('+') && mobile.length >= 10;
    },

    /**
     * Validar campos requeridos
     */
    validateRequired: (value) => {
        return value && value.toString().trim().length > 0;
    },

    /**
     * Validar longitud mínima
     */
    validateMinLength: (value, minLength) => {
        return value && value.toString().length >= minLength;
    },

    /**
     * Validar año de vehículo
     */
    validateVehicleYear: (year) => {
        const currentYear = new Date().getFullYear();
        const vehicleYear = parseInt(year);
        return vehicleYear >= 1900 && vehicleYear <= currentYear + 1;
    },

    /**
     * Validación para formulario de cliente
     */
    validateCustomerForm: (values) => {
        const errors = {};

        if (!validationHelpers.validateRequired(values.firstName)) {
            errors.firstName = 'El nombre es requerido';
        }

        if (!validationHelpers.validateRequired(values.lastName)) {
            errors.lastName = 'El apellido es requerido';
        }

        if (!validationHelpers.validateRequired(values.email)) {
            errors.email = 'El email es requerido';
        } else if (!validationHelpers.validateEmail(values.email)) {
            errors.email = 'El formato del email no es válido';
        }

        if (!validationHelpers.validateRequired(values.mobile)) {
            errors.mobile = 'El teléfono es requerido';
        } else if (!validationHelpers.validateMobile(values.mobile)) {
            errors.mobile = 'El teléfono debe incluir el código de país (ej: +52)';
        }

        if (values.password && !validationHelpers.validateMinLength(values.password, 6)) {
            errors.password = 'La contraseña debe tener al menos 6 caracteres';
        }

        return errors;
    },

    /**
     * Validación para formulario de vehículo
     */
    validateVehicleForm: (values) => {
        const errors = {};

        if (!validationHelpers.validateRequired(values.vehicleNumber)) {
            errors.vehicleNumber = 'El número de vehículo es requerido';
        }

        if (!validationHelpers.validateRequired(values.vehicleMake)) {
            errors.vehicleMake = 'La marca es requerida';
        }

        if (!validationHelpers.validateRequired(values.vehicleModel)) {
            errors.vehicleModel = 'El modelo es requerido';
        }

        if (!validationHelpers.validateRequired(values.carType)) {
            errors.carType = 'El tipo de vehículo es requerido';
        }

        if (!validationHelpers.validateRequired(values.vehicleColor)) {
            errors.vehicleColor = 'El color es requerido';
        }

        if (!validationHelpers.validateRequired(values.vehicleYear)) {
            errors.vehicleYear = 'El año es requerido';
        } else if (!validationHelpers.validateVehicleYear(values.vehicleYear)) {
            errors.vehicleYear = 'El año del vehículo no es válido';
        }

        if (!validationHelpers.validateRequired(values.stateLicensePlate)) {
            errors.stateLicensePlate = 'La placa estatal es requerida';
        }

        return errors;
    }
};

export default {
    useApiCall,
    useCustomerService,
    useDriverService,
    useVehicleService,
    useDeliveryService,
    useAllServices,
    useFormWithValidation,
    useAlert,
    validationHelpers
};
