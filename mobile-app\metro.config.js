const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Configuración para resolver problemas de dependencias
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Configuración para manejar módulos nativos
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// Configuración para excluir archivos problemáticos
config.resolver.blockList = [
  /.*\/node_modules\/.*\/node_modules\/react-native\/.*/,
];

// Configuración para transformaciones
config.transformer.minifierConfig = {
  keep_fnames: true,
  mangle: {
    keep_fnames: true,
  },
};

module.exports = config;