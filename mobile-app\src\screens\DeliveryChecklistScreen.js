import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    FlatList,
    TouchableOpacity,
    Alert,
    RefreshControl,
    ActivityIndicator
} from 'react-native';
import { Icon } from 'react-native-elements';
import { useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { colors } from '../common/theme';
import { api } from '../../../common/src/index';

const DeliveryChecklistScreen = () => {
    const navigation = useNavigation();
    const [deliveries, setDeliveries] = useState([]);
    const [loading, setLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);
    const [selectedDelivery, setSelectedDelivery] = useState(null);
    
    const { user } = useSelector(state => state.auth);
    const { language } = useSelector(state => state.language);

    // Cargar entregas con productos
    const loadDeliveries = async () => {
        try {
            setLoading(true);
            console.log('🔄 Cargando entregas con productos...');
            
            const firebase = api.firebase;
            if (!firebase) {
                console.error('❌ Firebase no disponible');
                Alert.alert('Error', 'No se pudo conectar con la base de datos');
                return;
            }

            // Obtener bookings del conductor actual
            const bookingsRef = firebase.database().ref('bookings');
            const snapshot = await bookingsRef.once('value');
            const bookingsData = snapshot.val();

            if (!bookingsData) {
                console.log('📭 No hay bookings disponibles');
                setDeliveries([]);
                setLoading(false);
                return;
            }

            // Filtrar bookings que tienen productos y están asignados al conductor
            const filteredBookings = Object.keys(bookingsData)
                .map(id => ({ ...bookingsData[id], id }))
                .filter(booking => {
                    const hasProducts = booking.orderDetails && booking.orderDetails.length > 0;
                    const hasDriver = booking.driver === user.id;
                    const isActiveStatus = ['NEW', 'ACCEPTED', 'ARRIVED', 'STARTED', 'PENDING', 'ASSIGNED', 'COMPLETE'].includes(booking.status);
                    const hasFolio = booking.folio;

                    console.log(`📋 Booking ${booking.id}:`, {
                        hasProducts,
                        hasDriver,
                        isActiveStatus,
                        hasFolio,
                        status: booking.status,
                        orderDetails: booking.orderDetails,
                        folio: booking.folio,
                        driver: booking.driver
                    });

                    return hasProducts && hasDriver && isActiveStatus;
                });

            console.log(`✅ Encontrados ${filteredBookings.length} bookings con productos`);

            // Convertir a formato de entregas
            const deliveriesList = filteredBookings.map(booking => ({
                id: booking.id,
                folio: booking.folio || `SIN-FOLIO-${booking.id}`,
                customerName: booking.customer_name || 'Cliente',
                pickupAddress: booking.pickupAddress || 'Sin dirección',
                dropAddress: booking.dropAddress || 'Sin dirección',
                status: booking.status,
                orderDetails: booking.orderDetails,
                products: parseOrderDetails(booking.orderDetails),
                createdAt: booking.bookingDate,
                completedAt: booking.endTime,
                driver: booking.driver,
                customer: booking.customer
            }));

            setDeliveries(deliveriesList);
            console.log(`📦 Entregas procesadas: ${deliveriesList.length}`);

        } catch (error) {
            console.error('❌ Error cargando entregas:', error);
            Alert.alert('Error', 'No se pudieron cargar las entregas');
        } finally {
            setLoading(false);
        }
    };

    // Parsear orderDetails para extraer productos
    const parseOrderDetails = (orderDetails) => {
        if (!orderDetails) return [];
        
        try {
            // Buscar productos en el formato "Productos: 4x Monitor, 1x Funda, 4x Teclado"
            const productsMatch = orderDetails.match(/Productos:\s*(.+)/);
            if (productsMatch) {
                const productsString = productsMatch[1];
                const products = productsString.split(',').map(product => {
                    const match = product.trim().match(/(\d+)x\s*(.+)/);
                    if (match) {
                        return {
                            id: `PROD-${Date.now()}-${Math.random()}`,
                            name: match[2].trim(),
                            quantity: parseInt(match[1]),
                            delivered: false,
                            deliveredAt: null
                        };
                    }
                    return {
                        id: `PROD-${Date.now()}-${Math.random()}`,
                        name: product.trim(),
                        quantity: 1,
                        delivered: false,
                        deliveredAt: null
                    };
                });
                return products;
            }
            
            // Si no hay formato específico, crear un producto genérico
            return [{
                id: `PROD-${Date.now()}`,
                name: orderDetails,
                quantity: 1,
                delivered: false,
                deliveredAt: null
            }];
        } catch (error) {
            console.error('❌ Error parseando productos:', error);
            return [{
                id: `PROD-${Date.now()}`,
                name: orderDetails || 'Producto sin especificar',
                quantity: 1,
                delivered: false,
                deliveredAt: null
            }];
        }
    };

    // Actualizar productos entregados
    const updateDeliveryProducts = async (deliveryId, updatedProducts) => {
        try {
            console.log(`🔄 Actualizando productos para entrega ${deliveryId}`);
            
            const firebase = api.firebase;
            if (!firebase) {
                console.error('❌ Firebase no disponible');
                return false;
            }

            const bookingRef = firebase.database().ref(`bookings/${deliveryId}`);
            
            // Actualizar productos en el booking
            await bookingRef.update({
                products: updatedProducts,
                productChecklistUpdated: true,
                productChecklistTimestamp: new Date().getTime()
            });

            console.log('✅ Productos actualizados exitosamente');
            
            // Actualizar estado local
            setDeliveries(prev => prev.map(delivery => 
                delivery.id === deliveryId 
                    ? { ...delivery, products: updatedProducts }
                    : delivery
            ));

            return true;
        } catch (error) {
            console.error('❌ Error actualizando productos:', error);
            Alert.alert('Error', 'No se pudieron actualizar los productos');
            return false;
        }
    };

    // Manejar completar entrega
    const handleCompleteDelivery = async (deliveryId) => {
        try {
            const delivery = deliveries.find(d => d.id === deliveryId);
            if (!delivery) return;

            const allProductsDelivered = delivery.products.every(product => product.delivered);
            
            if (!allProductsDelivered) {
                Alert.alert(
                    'Productos Pendientes',
                    'Debes marcar todos los productos como entregados antes de completar la entrega.',
                    [{ text: 'Entendido' }]
                );
                return;
            }

            // Actualizar estado del booking
            const firebase = api.firebase;
            if (firebase) {
                await firebase.database().ref(`bookings/${deliveryId}`).update({
                    status: 'COMPLETE',
                    deliveryCompletedAt: new Date().getTime()
                });
            }

            Alert.alert(
                'Entrega Completada',
                'Todos los productos han sido entregados exitosamente.',
                [{ text: 'OK' }]
            );

            // Recargar entregas
            loadDeliveries();

        } catch (error) {
            console.error('❌ Error completando entrega:', error);
            Alert.alert('Error', 'No se pudo completar la entrega');
        }
    };

    // Renderizar tarjeta de entrega
    const renderDeliveryCard = ({ item }) => {
        const deliveredCount = item.products.filter(p => p.delivered).length;
        const totalCount = item.products.length;
        const progress = totalCount > 0 ? (deliveredCount / totalCount) * 100 : 0;

        return (
            <TouchableOpacity
                style={styles.deliveryCard}
                onPress={() => setSelectedDelivery(item)}
            >
                <View style={styles.cardHeader}>
                    <View style={styles.folioContainer}>
                        <Icon
                            name="receipt"
                            type="material"
                            color={colors.PRIMARY}
                            size={20}
                        />
                        <Text style={styles.folioText}>{item.folio}</Text>
                    </View>
                    <View style={styles.statusContainer}>
                        <Text style={[
                            styles.statusText,
                            { color: item.status === 'COMPLETE' ? colors.GREEN : colors.ORANGE }
                        ]}>
                            {item.status}
                        </Text>
                    </View>
                </View>

                <View style={styles.cardBody}>
                    <View style={styles.customerInfo}>
                        <Icon
                            name="person"
                            type="material"
                            color={colors.GREY}
                            size={16}
                        />
                        <Text style={styles.customerName}>{item.customerName}</Text>
                    </View>

                    <View style={styles.addressInfo}>
                        <Icon
                            name="location-on"
                            type="material"
                            color={colors.GREY}
                            size={16}
                        />
                        <Text style={styles.addressText} numberOfLines={2}>
                            {item.dropAddress}
                        </Text>
                    </View>

                    <View style={styles.productsInfo}>
                        <Icon
                            name="inventory"
                            type="material"
                            color={colors.GREY}
                            size={16}
                        />
                        <Text style={styles.productsText}>
                            {deliveredCount}/{totalCount} productos entregados
                        </Text>
                    </View>

                    <View style={styles.progressContainer}>
                        <View style={styles.progressBar}>
                            <View 
                                style={[
                                    styles.progressFill,
                                    { width: `${progress}%` }
                                ]} 
                            />
                        </View>
                        <Text style={styles.progressText}>{Math.round(progress)}%</Text>
                    </View>
                </View>

                <View style={styles.cardFooter}>
                    <TouchableOpacity
                        style={styles.detailsButton}
                        onPress={() => setSelectedDelivery(item)}
                    >
                        <Text style={styles.detailsButtonText}>Ver Detalles</Text>
                    </TouchableOpacity>
                    
                    {progress === 100 && (
                        <TouchableOpacity
                            style={styles.completeButton}
                            onPress={() => handleCompleteDelivery(item.id)}
                        >
                            <Text style={styles.completeButtonText}>Completar</Text>
                        </TouchableOpacity>
                    )}
                </View>
            </TouchableOpacity>
        );
    };

    // Efectos
    useEffect(() => {
        loadDeliveries();
    }, []);

    const onRefresh = async () => {
        setRefreshing(true);
        await loadDeliveries();
        setRefreshing(false);
    };

    // Renderizar pantalla
    if (loading) {
        return (
            <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.PRIMARY} />
                <Text style={styles.loadingText}>Cargando entregas...</Text>
            </View>
        );
    }

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity
                    onPress={() => navigation.goBack()}
                    style={styles.backButton}
                >
                    <Icon
                        name="arrow-back"
                        type="material"
                        color={colors.WHITE}
                        size={24}
                    />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>Checklist de Reparto</Text>
            </View>

            {deliveries.length === 0 ? (
                <View style={styles.emptyContainer}>
                    <Icon
                        name="inventory"
                        type="material"
                        color={colors.GREY}
                        size={64}
                    />
                    <Text style={styles.emptyTitle}>No hay entregas pendientes</Text>
                    <Text style={styles.emptySubtitle}>
                        Las entregas con productos aparecerán aquí
                    </Text>
                    <TouchableOpacity
                        style={styles.refreshButton}
                        onPress={onRefresh}
                    >
                        <Text style={styles.refreshButtonText}>Recargar</Text>
                    </TouchableOpacity>
                </View>
            ) : (
                <FlatList
                    data={deliveries}
                    renderItem={renderDeliveryCard}
                    keyExtractor={item => item.id}
                    contentContainerStyle={styles.listContainer}
                    refreshControl={
                        <RefreshControl
                            refreshing={refreshing}
                            onRefresh={onRefresh}
                            colors={[colors.PRIMARY]}
                        />
                    }
                />
            )}

            {/* Modal de detalles */}
            {selectedDelivery && (
                <DeliveryDetailsModal
                    delivery={selectedDelivery}
                    onClose={() => setSelectedDelivery(null)}
                    onUpdateProducts={updateDeliveryProducts}
                />
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.BACKGROUND,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: colors.PRIMARY,
        paddingHorizontal: 16,
        paddingVertical: 12,
        elevation: 4,
    },
    backButton: {
        marginRight: 16,
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: colors.WHITE,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.BACKGROUND,
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
        color: colors.GREY,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 32,
    },
    emptyTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: colors.GREY,
        marginTop: 16,
        textAlign: 'center',
    },
    emptySubtitle: {
        fontSize: 16,
        color: colors.GREY,
        marginTop: 8,
        textAlign: 'center',
        marginBottom: 24,
    },
    refreshButton: {
        backgroundColor: colors.PRIMARY,
        paddingHorizontal: 24,
        paddingVertical: 12,
        borderRadius: 8,
    },
    refreshButtonText: {
        color: colors.WHITE,
        fontSize: 16,
        fontWeight: 'bold',
    },
    listContainer: {
        padding: 16,
    },
    deliveryCard: {
        backgroundColor: colors.WHITE,
        borderRadius: 12,
        marginBottom: 16,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    cardHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: colors.LIGHT_GREY,
    },
    folioContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    folioText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: colors.PRIMARY,
        marginLeft: 8,
    },
    statusContainer: {
        backgroundColor: colors.LIGHT_GREY,
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 4,
    },
    statusText: {
        fontSize: 12,
        fontWeight: 'bold',
    },
    cardBody: {
        padding: 16,
    },
    customerInfo: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
    },
    customerName: {
        fontSize: 16,
        fontWeight: 'bold',
        color: colors.BLACK,
        marginLeft: 8,
    },
    addressInfo: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginBottom: 8,
    },
    addressText: {
        fontSize: 14,
        color: colors.GREY,
        marginLeft: 8,
        flex: 1,
    },
    productsInfo: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
    },
    productsText: {
        fontSize: 14,
        color: colors.GREY,
        marginLeft: 8,
    },
    progressContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    progressBar: {
        flex: 1,
        height: 8,
        backgroundColor: colors.LIGHT_GREY,
        borderRadius: 4,
        marginRight: 12,
    },
    progressFill: {
        height: '100%',
        backgroundColor: colors.GREEN,
        borderRadius: 4,
    },
    progressText: {
        fontSize: 12,
        fontWeight: 'bold',
        color: colors.GREY,
        minWidth: 30,
    },
    cardFooter: {
        flexDirection: 'row',
        padding: 16,
        borderTopWidth: 1,
        borderTopColor: colors.LIGHT_GREY,
    },
    detailsButton: {
        flex: 1,
        backgroundColor: colors.PRIMARY,
        paddingVertical: 12,
        borderRadius: 8,
        marginRight: 8,
    },
    detailsButtonText: {
        color: colors.WHITE,
        fontSize: 14,
        fontWeight: 'bold',
        textAlign: 'center',
    },
    completeButton: {
        backgroundColor: colors.GREEN,
        paddingVertical: 12,
        paddingHorizontal: 16,
        borderRadius: 8,
    },
    completeButtonText: {
        color: colors.WHITE,
        fontSize: 14,
        fontWeight: 'bold',
    },
});

export default DeliveryChecklistScreen; 