import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    Modal,
    Alert,
    Image
} from 'react-native';
import { Icon, <PERSON>ton, Card, CheckBox } from 'react-native-elements';
import { colors } from '../common/theme';
import { fonts } from '../common/font';
import i18n from 'i18n-js';

export default function ProductChecklist({ 
    visible, 
    onClose, 
    order, 
    onUpdateProducts,
    deliveryId 
}) {
    const { t } = i18n;
    const isRTL = i18n.locale.indexOf('he') === 0 || i18n.locale.indexOf('ar') === 0;
    
    const [checkedProducts, setCheckedProducts] = useState({});
    const [allDelivered, setAllDelivered] = useState(false);
    const [deliveryNotes, setDeliveryNotes] = useState('');
    const [photoRequired, setPhotoRequired] = useState(false);
    const [signatureRequired, setSignatureRequired] = useState(false);

    // Inicializar estado de productos cuando se abre el modal
    useEffect(() => {
        if (visible && order?.products) {
            const initialState = {};
            order.products.forEach(product => {
                initialState[product.id] = product.delivered || false;
            });
            setCheckedProducts(initialState);
            
            // Verificar si todos los productos están entregados
            const allDeliveredCheck = order.products.every(product => 
                initialState[product.id] === true
            );
            setAllDelivered(allDeliveredCheck);
        }
    }, [visible, order]);

    // Manejar cambio en checkbox de producto individual
    const handleProductCheck = (productId, isChecked) => {
        const newCheckedProducts = {
            ...checkedProducts,
            [productId]: isChecked
        };
        setCheckedProducts(newCheckedProducts);

        // Verificar si todos los productos están marcados
        const allChecked = order.products.every(product => 
            newCheckedProducts[product.id] === true
        );
        setAllDelivered(allChecked);
    };

    // Marcar todos los productos como entregados/no entregados
    const handleSelectAll = (selectAll) => {
        const newCheckedProducts = {};
        order.products.forEach(product => {
            newCheckedProducts[product.id] = selectAll;
        });
        setCheckedProducts(newCheckedProducts);
        setAllDelivered(selectAll);
    };

    // Guardar cambios y cerrar modal
    const handleSaveAndClose = () => {
        // Validar que todos los productos estén marcados
        const allChecked = order.products.every(product => 
            checkedProducts[product.id] === true
        );

        if (!allChecked) {
            Alert.alert(
                'Productos Pendientes',
                'Debes marcar todos los productos como entregados antes de continuar.',
                [{ text: 'Entendido' }]
            );
            return;
        }

        // Actualizar productos con estado de entrega
        const updatedProducts = order.products.map(product => ({
            ...product,
            delivered: checkedProducts[product.id] || false,
            deliveredAt: checkedProducts[product.id] ? new Date().getTime() : null
        }));

        // Llamar función para actualizar en el componente padre
        onUpdateProducts(order.customerId, updatedProducts);
        
        Alert.alert(
            '✅ Entrega Completada',
            `Se han entregado ${Object.values(checkedProducts).filter(Boolean).length} de ${order.products.length} productos exitosamente.`,
            [{ text: 'OK', onPress: onClose }]
        );
    };

    // Calcular estadísticas
    const deliveredCount = Object.values(checkedProducts).filter(Boolean).length;
    const totalCount = order?.products?.length || 0;
    const progressPercentage = totalCount > 0 ? (deliveredCount / totalCount) * 100 : 0;

    if (!order) return null;

    return (
        <Modal
            visible={visible}
            animationType="slide"
            presentationStyle="pageSheet"
            onRequestClose={onClose}
        >
            <View style={styles.container}>
                {/* Header */}
                <View style={styles.header}>
                    <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                        <Icon name="close" type="material" size={24} color={colors.BLACK} />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>📋 Checklist de Productos</Text>
                    <View style={styles.progressContainer}>
                        <Text style={styles.progressText}>
                            {deliveredCount}/{totalCount} ({Math.round(progressPercentage)}%)
                        </Text>
                    </View>
                </View>

                {/* Información del Cliente */}
                <Card containerStyle={styles.customerCard}>
                    <View style={styles.customerInfo}>
                        <Text style={styles.customerName}>👤 {order.customerName}</Text>
                        <Text style={styles.customerPhone}>📞 {order.customerPhone}</Text>
                        <Text style={styles.deliveryAddress} numberOfLines={2}>
                            📍 {order.deliveryAddress.address}
                        </Text>
                    </View>
                </Card>

                {/* Controles de Selección */}
                <View style={styles.selectionControls}>
                    <TouchableOpacity
                        style={[styles.selectButton, { backgroundColor: colors.GREEN }]}
                        onPress={() => handleSelectAll(true)}
                    >
                        <Text style={styles.selectButtonText}>✅ Marcar Todos</Text>
                    </TouchableOpacity>
                    
                    <TouchableOpacity 
                        style={[styles.selectButton, { backgroundColor: colors.ORANGE }]}
                        onPress={() => handleSelectAll(false)}
                    >
                        <Text style={styles.selectButtonText}>❌ Desmarcar Todos</Text>
                    </TouchableOpacity>
                </View>

                {/* Lista de Productos */}
                <ScrollView style={styles.productsList}>
                    {order.products.map((product, index) => (
                        <Card key={product.id} containerStyle={styles.productCard}>
                            <View style={styles.productRow}>
                                <CheckBox
                                    checked={checkedProducts[product.id] || false}
                                    onPress={() => handleProductCheck(product.id, !checkedProducts[product.id])}
                                    containerStyle={styles.checkboxContainer}
                                    checkedColor={colors.GREEN}
                                />
                                
                                <View style={styles.productInfo}>
                                    <Text style={styles.productName}>📦 {product.name}</Text>
                                    <Text style={styles.productDetails}>
                                        SKU: {product.sku} | Cantidad: {product.quantity}
                                    </Text>
                                    {product.description && (
                                        <Text style={styles.productDescription}>
                                            📝 {product.description}
                                        </Text>
                                    )}
                                    <Text style={styles.productStatus}>
                                        Estado: {product.status || 'PENDING'}
                                    </Text>
                                </View>

                                <View style={styles.statusIndicator}>
                                    <Icon 
                                        name={checkedProducts[product.id] ? "check-circle" : "radio-button-unchecked"} 
                                        type="material" 
                                        size={24} 
                                        color={checkedProducts[product.id] ? colors.GREEN : colors.GREY} 
                                    />
                                </View>
                            </View>
                        </Card>
                    ))}
                </ScrollView>

                {/* Botón de Completar */}
                <View style={styles.footer}>
                    <Button
                        title={`✅ Completar Entrega (${deliveredCount}/${totalCount})`}
                        onPress={handleSaveAndClose}
                        disabled={!allDelivered}
                        buttonStyle={[
                            styles.completeButton,
                            { backgroundColor: allDelivered ? colors.GREEN : colors.GREY }
                        ]}
                        titleStyle={styles.completeButtonText}
                    />
                </View>
            </View>
        </Modal>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.BACKGROUND,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 16,
        backgroundColor: colors.WHITE,
        borderBottomWidth: 1,
        borderBottomColor: colors.BORDER_BACKGROUND,
    },
    closeButton: {
        padding: 8,
    },
    headerTitle: {
        fontSize: 18,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        flex: 1,
        textAlign: 'center',
    },
    progressContainer: {
        alignItems: 'center',
    },
    progressText: {
        fontSize: 14,
        fontFamily: fonts.Bold,
        color: colors.BLUE,
    },
    customerCard: {
        margin: 16,
        borderRadius: 12,
        elevation: 2,
    },
    customerInfo: {
        padding: 8,
    },
    customerName: {
        fontSize: 16,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginBottom: 4,
    },
    customerPhone: {
        fontSize: 14,
        fontFamily: fonts.Regular,
        color: colors.BLUE,
        marginBottom: 4,
    },
    deliveryAddress: {
        fontSize: 13,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        lineHeight: 18,
    },
    selectionControls: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        marginVertical: 10,
        marginHorizontal: 16,
    },
    selectButton: {
        flex: 1,
        paddingVertical: 10,
        paddingHorizontal: 15,
        borderRadius: 8,
        alignItems: 'center',
        marginHorizontal: 5,
    },
    selectButtonText: {
        color: colors.WHITE,
        fontSize: 14,
        fontFamily: fonts.Bold,
    },
    productsList: {
        flex: 1,
        paddingHorizontal: 16,
    },
    productCard: {
        marginBottom: 8,
        borderRadius: 8,
        elevation: 1,
    },
    productRow: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    checkboxContainer: {
        margin: 0,
        padding: 0,
        backgroundColor: 'transparent',
        borderWidth: 0,
    },
    productInfo: {
        flex: 1,
        marginLeft: 8,
    },
    productName: {
        fontSize: 15,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginBottom: 2,
    },
    productDetails: {
        fontSize: 13,
        fontFamily: fonts.Regular,
        color: colors.BLUE,
        marginBottom: 2,
    },
    productDescription: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        marginBottom: 2,
        fontStyle: 'italic',
    },
    productStatus: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.ORANGE,
        fontWeight: 'bold',
    },
    statusIndicator: {
        marginLeft: 8,
    },
    footer: {
        paddingHorizontal: 16,
        paddingVertical: 16,
        borderTopWidth: 1,
        borderTopColor: colors.BORDER_BACKGROUND,
        backgroundColor: colors.WHITE,
    },
    completeButton: {
        borderRadius: 8,
        paddingVertical: 16,
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: 50,
    },
    completeButtonText: {
        color: colors.WHITE,
        fontSize: 16,
        fontWeight: '700',
        textAlign: 'center',
        letterSpacing: 0.5,
    },
});
