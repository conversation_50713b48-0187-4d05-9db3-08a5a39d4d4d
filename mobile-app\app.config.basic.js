export default {
    name: "Balle Movil",
    description: "Aplicación de transporte",
    owner: "balle-comercial",
    slug: "balle-movil",
    scheme: "balle-movil",
    privacy: "public",
    runtimeVersion: "1.0.0",
    platforms: ["ios", "android"],
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/images/logo1024x1024.png",
    splash: {
        "image": "./assets/images/splash.png",
        "resizeMode":'cover',
        "backgroundColor": "#ffffff"
    },
    updates: {
        "fallbackToCacheTimeout": 0,
        "url": "https://u.expo.dev/your-project-id",
    },
    extra: {
        eas: {
          projectId: "your-project-id"
        }
    },
    assetBundlePatterns: ["**/*"],
    ios: {
        supportsTablet: true,
        bundleIdentifier: "com.balle.movil",
        infoPlist: {
            "NSLocationWhenInUseUsageDescription": "For a reliable ride, App collects location data from the time you open the app until a trip ends. This improves pickups, support, and more.",
            "NSCameraUsageDescription": "This app uses the camera to take your profile picture.",
            "NSPhotoLibraryUsageDescription": "This app uses Photo Library for uploading your profile picture.",
        },
        buildNumber: "1.0.0"
    },
    android: {
        package: "com.balle.movil",
        versionCode: 1,
        permissions: [
            "CAMERA",
            "READ_EXTERNAL_STORAGE",
            "WRITE_EXTERNAL_STORAGE",
            "ACCESS_FINE_LOCATION",
            "ACCESS_COARSE_LOCATION",
            "CAMERA_ROLL",
            "FOREGROUND_SERVICE",
            "FOREGROUND_SERVICE_LOCATION",
            "ACCESS_BACKGROUND_LOCATION"
        ],
        blockedPermissions:["com.google.android.gms.permission.AD_ID"],
        googleServicesFile: "./google-services.json"
    },
    plugins: [
        "expo-asset",
        "expo-font"
    ]
}; 