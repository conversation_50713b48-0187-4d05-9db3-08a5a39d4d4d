import { AppConfig } from './config/AppConfig';
import { GoogleMapApiConfig } from './config/GoogleMapApiConfig';

export default {
    name: AppConfig.app_name,
    description: AppConfig.app_description,
    owner: AppConfig.expo_owner,
    slug: AppConfig.expo_slug,
    scheme: AppConfig.expo_slug,
    privacy: "public",
    runtimeVersion: AppConfig.ios_app_version,
    platforms: ["ios", "android"],
    version: AppConfig.ios_app_version,
    orientation: "portrait",
    icon: "./assets/images/logo1024x1024.png",
    splash: {
        "image": "./assets/images/splash.png",
        "resizeMode":'cover',
        "backgroundColor": "#ffffff"
    },
    updates: {
        "fallbackToCacheTimeout": 0,
        "url": "https://u.expo.dev/" + AppConfig.expo_project_id,
    },
    extra: {
        eas: {
          projectId: AppConfig.expo_project_id
        }
    },
    assetBundlePatterns: ["**/*"],
    ios: {
        supportsTablet: true,
        bundleIdentifier: AppConfig.app_identifier,
        infoPlist: {
            "NSLocationWhenInUseUsageDescription": "For a reliable ride, App collects location data from the time you open the app until a trip ends. This improves pickups, support, and more.",
            "NSCameraUsageDescription": "This app uses the camera to take your profile picture.",
            "NSPhotoLibraryUsageDescription": "This app uses Photo Library for uploading your profile picture.",
        },
        config: {
            googleMapsApiKey: GoogleMapApiConfig.ios
        },
        googleServicesFile: "./GoogleService-Info.plist",
        buildNumber: AppConfig.ios_app_version
    },
    android: {
        package: AppConfig.app_identifier,
        versionCode: AppConfig.android_app_version,
        permissions: [
            "CAMERA",
            "READ_EXTERNAL_STORAGE",
            "WRITE_EXTERNAL_STORAGE",
            "ACCESS_FINE_LOCATION",
            "ACCESS_COARSE_LOCATION",
            "CAMERA_ROLL",
            "FOREGROUND_SERVICE",
            "FOREGROUND_SERVICE_LOCATION",
            "ACCESS_BACKGROUND_LOCATION"
        ],
        blockedPermissions:["com.google.android.gms.permission.AD_ID"],
        googleServicesFile: "./google-services.json",
        config: {
            googleMaps: {
                apiKey: GoogleMapApiConfig.android
            }
        }
    },
    plugins: [
        "expo-asset",
        "expo-font",
        [
            "expo-build-properties",
            {
              "ios": {
                "useFrameworks": "static"
              },
              "android": {
                "compileSdkVersion": 34,
                "targetSdkVersion": 34,
                "buildToolsVersion": "34.0.0"
              }
            }
        ]
    ]
}; 