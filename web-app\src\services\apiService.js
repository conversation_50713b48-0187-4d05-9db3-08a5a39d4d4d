/**
 * Servicio API para consumir los endpoints de Firebase Functions
 * Basado en la colección de Postman Apis-Art
 */

import { FirebaseConfig } from '../config/FirebaseConfig';

// URL base de las Firebase Functions
const API_BASE_URL = `https://us-central1-${FirebaseConfig.projectId}.cloudfunctions.net`;

/**
 * Función helper para realizar peticiones HTTP
 * @param {string} endpoint - Endpoint de la API
 * @param {object} options - Opciones de la petición (method, body, headers)
 * @returns {Promise} - Respuesta de la API
 */
const apiRequest = async (endpoint, options = {}) => {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type'
        }
    };

    const requestOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };

    try {
        const response = await fetch(url, requestOptions);
        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || `HTTP error! status: ${response.status}`);
        }

        return data;
    } catch (error) {
        console.error(`Error en ${endpoint}:`, error);
        throw error;
    }
};

/**
 * Servicios para gestión de clientes
 */
export const customerService = {
    /**
     * Crear un nuevo cliente
     * @param {object} customerData - Datos del cliente
     * @returns {Promise} - Respuesta de la API
     */
    create: async (customerData) => {
        const payload = {
            email: customerData.email,
            mobile: customerData.mobile,
            password: customerData.password,
            firstName: customerData.firstName,
            lastName: customerData.lastName,
            nombreComercial: customerData.nombreComercial || '',
            rfc: customerData.rfc || '',
            city: customerData.city || ''
        };

        return apiRequest('/createUser', {
            method: 'POST',
            body: JSON.stringify(payload)
        });
    },

    /**
     * Actualizar un cliente existente
     * @param {object} customerData - Datos del cliente a actualizar
     * @returns {Promise} - Respuesta de la API
     */
    update: async (customerData) => {
        const payload = {
            uid: customerData.uid,
            firstName: customerData.firstName,
            lastName: customerData.lastName,
            email: customerData.email,
            mobile: customerData.mobile,
            nombreComercial: customerData.nombreComercial || '',
            rfc: customerData.rfc || '',
            city: customerData.city || ''
        };

        return apiRequest('/updateCustomer', {
            method: 'POST',
            body: JSON.stringify(payload)
        });
    }
};

/**
 * Servicios para gestión de conductores
 */
export const driverService = {
    /**
     * Crear un nuevo conductor
     * @param {object} driverData - Datos del conductor
     * @returns {Promise} - Respuesta de la API
     */
    create: async (driverData) => {
        const payload = {
            firstName: driverData.firstName,
            lastName: driverData.lastName,
            email: driverData.email,
            mobile: driverData.mobile,
            password: driverData.password || undefined, // Opcional
            hireDate: driverData.hireDate || Date.now() // Opcional
        };

        return apiRequest('/createDriver', {
            method: 'POST',
            body: JSON.stringify(payload)
        });
    },

    /**
     * Actualizar un conductor existente
     * @param {object} driverData - Datos del conductor a actualizar
     * @returns {Promise} - Respuesta de la API
     */
    update: async (driverData) => {
        const payload = {
            uid: driverData.uid,
            firstName: driverData.firstName,
            lastName: driverData.lastName,
            email: driverData.email,
            mobile: driverData.mobile
        };

        return apiRequest('/updateDriver', {
            method: 'PUT',
            body: JSON.stringify(payload)
        });
    }
};

/**
 * Servicios para gestión de vehículos
 */
export const vehicleService = {
    /**
     * Crear un nuevo vehículo
     * @param {object} vehicleData - Datos del vehículo
     * @returns {Promise} - Respuesta de la API
     */
    create: async (vehicleData) => {
        const payload = {
            vehicleNumber: vehicleData.vehicleNumber,
            vehicleMake: vehicleData.vehicleMake,
            vehicleModel: vehicleData.vehicleModel,
            axles: vehicleData.axles || 2,
            capacity: vehicleData.capacity || 0,
            carType: vehicleData.carType,
            vehicleColor: vehicleData.vehicleColor,
            vehicleYear: vehicleData.vehicleYear,
            stateLicensePlate: vehicleData.stateLicensePlate
        };

        return apiRequest('/createVehicle', {
            method: 'POST',
            body: JSON.stringify(payload)
        });
    },

    /**
     * Actualizar un vehículo existente
     * @param {object} vehicleData - Datos del vehículo a actualizar
     * @returns {Promise} - Respuesta de la API
     */
    update: async (vehicleData) => {
        const payload = {
            vehicleId: vehicleData.vehicleId,
            vehicleNumber: vehicleData.vehicleNumber,
            vehicleMake: vehicleData.vehicleMake,
            vehicleModel: vehicleData.vehicleModel,
            carType: vehicleData.carType,
            car_image: vehicleData.car_image,
            other_info: vehicleData.other_info,
            active: vehicleData.active,
            approved: vehicleData.approved,
            driver: vehicleData.driver,
            unitNumber: vehicleData.unitNumber,
            stateLicensePlate: vehicleData.stateLicensePlate,
            vehicleColor: vehicleData.vehicleColor,
            vehicleYear: vehicleData.vehicleYear,
            vehicleCategory: vehicleData.vehicleCategory,
            federalLicensePlate: vehicleData.federalLicensePlate,
            vehicleClass: vehicleData.vehicleClass,
            vehicleType: vehicleData.vehicleType,
            vehicleNotes: vehicleData.vehicleNotes
        };

        return apiRequest('/updateVehicle', {
            method: 'PUT',
            body: JSON.stringify(payload)
        });
    }
};

/**
 * Servicios para gestión de entregas agrupadas
 */
export const deliveryService = {
    /**
     * Crear una nueva entrega agrupada
     * @param {object} deliveryData - Datos de la entrega agrupada
     * @returns {Promise} - Respuesta de la API
     */
    createGroupedDelivery: async (deliveryData) => {
        const payload = {
            folio: deliveryData.folio,
            driver: deliveryData.driver,
            vehicle: deliveryData.vehicle,
            orders: deliveryData.orders,
            estimatedRoute: deliveryData.estimatedRoute || [],
            totalDistance: deliveryData.totalDistance || 0,
            totalEstimatedTime: deliveryData.totalEstimatedTime || 0
        };

        return apiRequest('/receiveGroupedDelivery', {
            method: 'POST',
            body: JSON.stringify(payload)
        });
    }
};

/**
 * Función helper para manejar errores de API
 * @param {Error} error - Error capturado
 * @param {string} context - Contexto donde ocurrió el error
 */
export const handleApiError = (error, context = '') => {
    console.error(`Error en ${context}:`, error);
    
    // Aquí puedes agregar lógica adicional para manejar errores
    // como mostrar notificaciones, logging, etc.
    
    return {
        success: false,
        error: error.message || 'Error desconocido',
        context
    };
};

/**
 * Exportación por defecto con todos los servicios
 */
export default {
    customer: customerService,
    driver: driverService,
    vehicle: vehicleService,
    delivery: deliveryService,
    handleApiError
};
